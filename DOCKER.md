# MinerU Service Docker 部署指南

本文档介绍如何使用 Docker 部署 MinerU Service GPU 版本。

## 📋 目录

- [快速开始](#快速开始)
- [构建镜像](#构建镜像)
- [运行容器](#运行容器)
- [Docker Compose](#docker-compose)
- [配置说明](#配置说明)
- [故障排除](#故障排除)

## 🚀 快速开始

### 使用 Docker Compose（推荐）

```bash
# GPU版本（需要NVIDIA Docker支持）
docker-compose up -d
```

### 使用 Makefile

```bash
# 构建并运行GPU版本
make docker-build
make docker-run
```

## 🔨 构建镜像

### 使用构建脚本

```bash
# 构建GPU版本
./bin/docker-build.sh --type gpu

# 指定版本号
./bin/docker-build.sh --type gpu --version 1.3.10
```

### 手动构建

```bash
# GPU版本
docker build -f deploy/Dockerfile-gpu -t mineru-service:1.3.10-gpu .
```

## 🏃 运行容器

### 使用运行脚本

```bash
# 运行GPU版本
./bin/docker-run.sh --type gpu

# 自定义端口
./bin/docker-run.sh --type gpu --port 8082

# 前台运行
./bin/docker-run.sh --type gpu --foreground
```

### 手动运行

```bash
# GPU版本
docker run -d \
  --name mineru-service \
  --gpus all \
  -p 8081:8081 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/config:/app/config \
  -e DEVICE=cuda \
  mineru-service:1.3.10-gpu
```

## 🐳 Docker Compose

### 配置文件

项目包含 `docker-compose.yml` 文件，支持以下服务：

- `mineru-service`: GPU版本服务（端口8081）

### 常用命令

```bash
# 启动GPU版本
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps
```

## ⚙️ 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `DEBUG` | `false` | 调试模式 |
| `SERVER_HOST` | `0.0.0.0` | 服务器地址 |
| `SERVER_PORT` | `8081` | 服务器端口 |
| `SERVER_WORKERS` | `2` | 工作进程数 |
| `DEVICE` | `cuda` | 设备类型 |
| `CUDA_VISIBLE_DEVICES` | `0` | GPU设备ID |

### 卷挂载

| 容器路径 | 主机路径 | 说明 |
|----------|----------|------|
| `/app/data` | `./data` | 数据目录 |
| `/app/config` | `./config` | 配置文件 |
| `/app/confs` | `./confs` | 配置文件 |

### 端口映射

- GPU版本: `8081:8081`

## 🔍 健康检查

容器包含健康检查功能：

```bash
# 检查容器健康状态
docker ps

# 手动健康检查
curl http://localhost:8081/health
```

## 📝 日志管理

```bash
# 查看容器日志
docker logs -f mineru-service

# 使用Docker Compose查看日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f mineru-service
```

## 🛠️ 故障排除

### 常见问题

1. **GPU支持问题**
   ```bash
   # 检查NVIDIA Docker支持
   docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
   ```

2. **端口占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep 8081

   # 使用不同端口
   ./bin/docker-run.sh --type gpu --port 8082
   ```

3. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x bin/docker-build.sh bin/docker-run.sh
   ```

4. **镜像构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -f

   # 重新构建
   docker build --no-cache -f deploy/Dockerfile-gpu -t mineru-service:latest-gpu .
   ```

### 清理命令

```bash
# 停止并删除容器
make docker-stop

# 清理镜像和容器
make docker-clean

# 完全清理Docker系统
docker system prune -a -f
```

## 📚 更多信息

- [API文档](http://localhost:8081/docs)
- [健康检查](http://localhost:8081/health)
- [项目README](README.md)
