FROM nvidia/cuda:12.4.0-devel-ubuntu22.04
ARG VERSION=1.3.10

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG="en_US.UTF-8" \
    TZ="Asia/Shanghai" \
    CUDA_VISIBLE_DEVICES=0

# 安装Python和系统依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-dev \
    python3-distutils \
    python3-pip \
    build-essential \
    curl \
    git \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 创建Python符号链接
RUN ln -sf /usr/bin/python3 /usr/bin/python

# 升级 pip
RUN pip install --upgrade pip

# 安装 uv
RUN pip install uv

# 使用 uv 安装 magic-pdf[full] (允许预发布版本)
RUN uv pip install --system -U "magic-pdf[full]==1.3.10" --prerelease=allow


# 创建应用目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt requirements-gpu.txt ./

# 安装Python依赖（GPU版本）
RUN uv pip install --system -r requirements.txt --index https://pypi.tuna.tsinghua.edu.cn/simple && \
    uv pip install --system -r requirements-gpu.txt --index https://pypi.tuna.tsinghua.edu.cn/simple

# 复制应用代码
COPY src/ ./src/
COPY config/ ./config/
COPY confs/ ./confs/
COPY bin/ ./bin/
COPY .env.example ./

# 复制magic-pdf配置文件到期望的位置
COPY confs/magic-pdf.json /root/magic-pdf.json

# 设置magic-pdf配置文件路径环境变量
ENV MAGIC_PDF_CONFIG_PATH=/root/magic-pdf.json

# 设置权限
RUN chmod +x bin/start.sh && \
    ln -sf /app/.env.example /app/.env

# 创建数据目录
RUN mkdir -p /app/data

# 暴露端口
EXPOSE 8081

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/health || exit 1

# 运行应用
CMD ["/app/bin/start.sh"]
