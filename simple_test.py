#!/usr/bin/env python3
"""
简单的图像处理测试
"""

import sys
import os
import tempfile
from pathlib import Path
from PIL import Image
import io

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_image_bytes():
    """测试图像字节数据处理"""
    print("开始测试图像字节数据处理...")
    
    # 创建一个测试图像
    img = Image.new('RGB', (100, 100), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    image_bytes = buffer.getvalue()
    
    print(f"创建了 {len(image_bytes)} 字节的图像数据")
    
    # 测试能否重新打开
    try:
        test_img = Image.open(io.BytesIO(image_bytes))
        print(f"成功打开图像，尺寸: {test_img.size}")
        return True
    except Exception as e:
        print(f"打开图像失败: {e}")
        return False

def test_invalid_bytes():
    """测试无效字节数据"""
    print("测试无效字节数据...")
    
    invalid_bytes = b"this is not image data"
    
    try:
        test_img = Image.open(io.BytesIO(invalid_bytes))
        print("意外成功打开了无效数据")
        return False
    except Exception as e:
        print(f"正确地拒绝了无效数据: {e}")
        return True

if __name__ == "__main__":
    print("开始简单测试...")
    
    success1 = test_image_bytes()
    success2 = test_invalid_bytes()
    
    if success1 and success2:
        print("所有测试通过!")
        sys.exit(0)
    else:
        print("测试失败!")
        sys.exit(1)
