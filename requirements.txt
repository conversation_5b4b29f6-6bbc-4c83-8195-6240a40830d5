# Core web framework dependencies
fastapi==0.116.1
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# Configuration and validation
pydantic>=2.5.0
pydantic-settings>=2.1.0
python-dotenv>=1.0.0

# Logging
loguru>=0.7.0

# HTTP client for API calls
httpx>=0.25.0

# Service discovery
python-consul>=1.1.0

# Note: magic-pdf[full] will be installed via Dockerfile with --prerelease=allow
# This will automatically install all ML dependencies including:
# - torch, torchvision, transformers, tokenizers
# - opencv-python, numpy
# - ultralytics, doclayout-yolo
# - boto3, Brotli, click, fast-langdetect, scikit-learn, tqdm, PyMuPDF
# - pdfminer-six, sympy, and other dependencies
