import logging
import uuid
import inspect
from typing import List, Any, Optional, Union

class DLog:
    """日志类"""
    
    def __init__(self, trace_id: Optional[str] = None):
        """
        初始化DLog实例
        
        Args:
            trace_id: 可选的唯一标识符，用于兼容服务端传过来trace_id的值
        """
        self.trace_id = trace_id
        self.uuid = uuid.uuid4() if not trace_id else None
    
    @staticmethod
    def get_level(level_param: str) -> int:
        """
        解析日志级别字符串，返回对应的logging级别
        
        Args:
            level_param: 日志级别字符串(debug/info/warn/error/fatal/panic)
        
        Returns:
            对应的logging级别
        """
        level_mapping = {
            'debug': logging.DEBUG,
            'info': logging.INFO,
            'warn': logging.WARN,
            'error': logging.ERROR,
            'fatal': logging.FATAL,
            'panic': logging.CRITICAL  # Python中没有完全对应的PANIC级别，使用CRITICAL替代
        }
        return level_mapping.get(level_param.lower(), logging.INFO)
    
    @staticmethod
    def set_log(level: int) -> None:
        formatter = logging.Formatter(
            fmt='{"log_level": "%(levelname)s", "timestamp": "%(asctime)s", "uuid": "%(uuid)s", "file": "%(custom_filename)s", "line": %(custom_lineno)d, "message": "%(message)s"}',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        root_logger.handlers = []
        
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    def _get_base_msg(self) -> dict:
        """获取日志基础前缀信息，包含文件、行号和唯一标识符"""
        frame = inspect.currentframe()
        if frame:
            # 跳过当前方法和调用它的方法
            frame = frame.f_back
            while frame:
                # 检查文件名是否属于dlog包
                filename = inspect.getframeinfo(frame).filename
                if 'dlog' not in filename:
                    break
                frame = frame.f_back
            
            if frame:
                caller_info = inspect.getframeinfo(frame)
                file_name = caller_info.filename.split('\\')[-1]
                line_number = caller_info.lineno
            else:
                file_name, line_number = 'unknown', 0
        else:
            file_name, line_number = 'unknown', 0
        
        return {
            'uuid': self.trace_id or str(self.uuid),
            'custom_filename': file_name,
            'custom_lineno': line_number
        }
    
    def get_uuid(self) -> str:
        """获取唯一标识符字符串"""
        return self.trace_id if self.trace_id else str(self.uuid)
    
    def _log(self, level: int, msg: str, *args: Any, **kwargs: Any) -> None:
        """通用日志记录方法"""
        # 获取日志上下文
        extra = self._get_base_msg()
        
        # 记录日志
        logging.log(level, msg, *args, extra=extra, **kwargs)
    
    # 格式化日志方法
    def debugf(self, format_str: str, *args: Any) -> None:
        self._log(logging.DEBUG, format_str, *args)
    
    def printf(self, format_str: str, *args: Any) -> None:
        self._log(logging.INFO, format_str, *args)
    
    def infof(self, format_str: str, *args: Any) -> None:
        self._log(logging.INFO, format_str, *args)
    
    def warnf(self, format_str: str, *args: Any) -> None:
        self._log(logging.WARN, format_str, *args)
    
    def warningf(self, format_str: str, *args: Any) -> None:
        self._log(logging.WARN, format_str, *args)
    
    def errorf(self, format_str: str, *args: Any) -> None:
        self._log(logging.ERROR, format_str, *args)
    
    def panicf(self, format_str: str, *args: Any) -> None:
        self._log(logging.CRITICAL, format_str, *args)
        raise SystemExit(1)  # 模拟Panic，记录后退出
    
    def fatalf(self, format_str: str, *args: Any) -> None:
        self._log(logging.FATAL, format_str, *args)
        raise SystemExit(1)  # 记录后退出
    
    # 无格式日志方法
    def debugln(self, *args: Any) -> None:
        self._log(logging.DEBUG, ' '.join(map(str, args)))
    
    def println(self, *args: Any) -> None:
        self._log(logging.INFO, ' '.join(map(str, args)))
    
    def infoln(self, *args: Any) -> None:
        self._log(logging.INFO, ' '.join(map(str, args)))
    
    def warnln(self, *args: Any) -> None:
        self._log(logging.WARN, ' '.join(map(str, args)))
    
    def warningln(self, *args: Any) -> None:
        self._log(logging.WARN, ' '.join(map(str, args)))
    
    def errorln(self, *args: Any) -> None:
        self._log(logging.ERROR, ' '.join(map(str, args)))
    
    def panicln(self, *args: Any) -> None:
        self._log(logging.CRITICAL, ' '.join(map(str, args)))
        raise SystemExit(1)  # 模拟Panic，记录后退出
    
    def fatalln(self, *args: Any) -> None:
        self._log(logging.FATAL, ' '.join(map(str, args)))
        raise SystemExit(1)  # 记录后退出

# 使用示例
if __name__ == "__main__":
    # 设置日志级别为DEBUG
    DLog.set_log(DLog.get_level("debug"))
    
    # 创建带有自动生成UUID的日志实例
    log1 = DLog()
    log1.infof("这是一条带有格式的信息日志: %s", "参数")
    log1.debugln("这是一条无格式的调试日志", "多个参数")
    
    # 创建带有自定义唯一标识符的日志实例
    log2 = DLog("custom-id-123")
    log2.errorf("这是一条错误日志: %d", 123)
    
    # 测试所有日志级别
    log1.debugf("调试日志示例")
    log1.printf("普通输出示例")
    log1.infof("信息日志示例")
    log1.warnf("警告日志示例")
    log1.warningf("警告日志示例(别名)")
    log1.errorf("错误日志示例")
    
    # 注意：panicf和fatalln会导致程序退出，取消注释以下代码进行测试
    # log1.panicf("严重错误，程序将退出")
    # log1.fatalln("致命错误，程序将退出")