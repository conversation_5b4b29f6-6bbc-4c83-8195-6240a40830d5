from service.base_service import BaseService
from schema.schema import Request, Response
from utils.common_utils import cost_time, singleton_func
from core.settings import settings


@singleton_func
class ExampleService(BaseService):
    def __init__(self):
        super().__init__()
        self.name = "example_service"

    def initialize(self):
        pass

    @cost_time
    async def func1(self, request: Request) -> Response:
        """
        计算query的embedding

        Args:
            request (EmbeddingRequest): 请求对象

        Returns:
            EmbeddingResponse: 响应对象
        """
        response = Response(
            request_id=request.request_id,
            embedding=[0.1] * 768,  # 示例嵌入向量
        )
        return response

    @cost_time
    async def func2(self, request: Request) -> Response:
        """
        计算item的embedding

        Args:
            request (EmbeddingRequest): 请求对象

        Returns:
            EmbeddingResponse: 响应对象
        """
        response = Response(
            request_id=request.request_id,
            embedding=[0.1] * 768,  # 示例嵌入向量
        )
        return response
    

example_server = ExampleService()


if __name__ == "__main__":
    import asyncio
    import os
    from algo_base.dlog import DLog

    dlog = DLog()
    os.environ["TRANSFORMERS_OFFLINE"] = "1"

    dlog.infoln("Starting ExampleService test...")

    example_service = ExampleService()
    example_service.initialize()
    request = Request(
        request_id="123",
        text="hello",
    )
    response = asyncio.run(example_service.func1(request))
    dlog.infof("Response 1: %s", str(response))

    request = Request(
        request_id="123",
        text="hello",
        image_url="https://biz-idphoto-pre.meitudata.com/wearm-public/chenshan/5.jpg",
    )
    response = asyncio.run(example_service.func2(request))
    dlog.infof("Response 2: %s", str(response))

    dlog.infoln("ExampleService test finished.")
 