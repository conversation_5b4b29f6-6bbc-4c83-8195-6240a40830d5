import time
import tempfile
from pathlib import Path
from typing import List
from algo_base.dlog import DLog
from service.base_service import BaseService
from core.mineru import MineruBase
from schema.schema import  TextBoxResponse, TextBox
from utils.common_utils import cost_time, singleton_func
from fastapi import UploadFile
from pathlib import Path

@singleton_func
class TextExtractionService(BaseService):
    """
    文本提取服务类
    负责处理图片的文本框提取
    """

    def __init__(self):
        self.dlog = DLog()
        self.name = "text_extraction_service"
        self.mineru_base = None
        self.initialized = False
        super().__init__()
        
    def initialize(self):
        """
        初始化文本提取服务
        """
        if self.initialized:
            return

        try:
            # 初始化 MinerU 基础类
            self.mineru_base = MineruBase()

            self.initialized = True
        except Exception as e:
            self.dlog.errorf("TextExtractionService初始化失败: %s", str(e))
            raise

    def _convert_text_box_format(self, raw_text_boxes: List[dict]) -> List[TextBox]:
        """
        将原始文本框数据转换为标准格式
        
        Args:
            raw_text_boxes: 原始文本框数据
            
        Returns:
            标准化的文本框列表
        """
        text_boxes = []
        
        for raw_box in raw_text_boxes:
            try:
                text_box = TextBox(
                    page_idx=raw_box.get('page_idx', 0),
                    bbox=raw_box.get('bbox'),
                    text=raw_box.get('text'),
                    type=raw_box.get('type'),
                    page_size=raw_box.get('page_size')
                )
                text_boxes.append(text_box)
            except Exception as e:
                self.dlog.warnf("转换文本框格式失败: %s, 原始数据: %s", str(e), str(raw_box))
                continue
                
        return text_boxes

    @cost_time
    async def extract_text_boxes(self) -> TextBoxResponse:
        """
        提取图片中的文本框
        
        Args:
            request: 文本框提取请求
            
        Returns:
            文本框提取响应
        """
        start_time = time.time()
        
        try:
            if not self.initialized:
                raise Exception("TextExtractionService未初始化")
                
            # 这个方法现在已经废弃，请使用 extract_text_boxes_from_files
            raise Exception("此方法已废弃，请使用 extract_text_boxes_from_files 方法")
                
        except Exception as e:
            self.dlog.errorf("文本框提取失败: %s", str(e))
            processing_time = time.time() - start_time
            
            return TextBoxResponse(
                text_boxes=[],
                total_count=0,
                processing_time=processing_time,
                error_msg=str(e)
            )

    @cost_time
    async def extract_text_boxes_from_files(self, uploaded_files: List[UploadFile]) -> TextBoxResponse:
        """
        从上传的文件中提取文本框

        Args:
            uploaded_files: 上传的文件列表
            request: 文本框提取请求

        Returns:
            文本框提取响应
        """
        start_time = time.time()
        temp_dir = r"D:\py\mineru-service-1.3.10\data"
        try:
            if not self.initialized:
                raise Exception("TextExtractionService未初始化")

            if not uploaded_files:
                raise ValueError("上传文件列表不能为空")

            # 创建临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                # 读取上传的文件并转换为字节数据
                image_bytes_list = []

                for i, uploaded_file in enumerate(uploaded_files):
                    try:
                        # 读取文件内容
                        file_content = await uploaded_file.read()
                        image_bytes_list.append(file_content)

                        self.dlog.infof("成功读取文件: %s, 大小: %d bytes", uploaded_file.filename, len(file_content))
                    except Exception as e:
                        self.dlog.errorf("读取文件失败，跳过: %s, 错误: %s", uploaded_file.filename, str(e))
                        continue

                if not image_bytes_list:
                    raise ValueError("没有成功读取任何图片文件")

                # 设置输出目录
                if temp_dir:
                    img_folder = Path(temp_dir)
                else:
                    img_folder = Path(temp_dir) / "output"

                img_folder.mkdir(parents=True, exist_ok=True)

                # 调用 MinerU 进行文本框提取，传入图片字节数据列表
                raw_text_boxes = self.mineru_base.gen_text_boxes(image_bytes_list, img_folder)

                # 转换格式
                text_boxes = self._convert_text_box_format(raw_text_boxes)

                processing_time = time.time() - start_time

                response = TextBoxResponse(
                    text_boxes=text_boxes,
                    total_count=len(text_boxes),
                    processing_time=processing_time
                )

                # 打印返回数据的详细日志
                self._print_response_log(response)

                return response

        except Exception as e:
            self.dlog.errorf("文本框提取失败: %s", str(e))
            processing_time = time.time() - start_time

            error_response = TextBoxResponse(
                text_boxes=[],
                total_count=0,
                processing_time=processing_time,
                error_msg=str(e)
            )

            # 打印错误响应日志
            self._print_response_log(error_response)

            return error_response

    def _print_response_log(self, response: TextBoxResponse):
        """
        打印API响应的详细日志
        """
        try:
            # 基本统计信息
            if response.text_boxes:
                # 按类型统计
                type_counts = {}
                for text_box in response.text_boxes:
                    text_type = text_box.type or 'unknown'
                    type_counts[text_type] = type_counts.get(text_type, 0) + 1
        except Exception as e:
            self.dlog.errorf("打印响应日志失败: %s", str(e))

# 创建全局实例
text_extraction_service = TextExtractionService()
