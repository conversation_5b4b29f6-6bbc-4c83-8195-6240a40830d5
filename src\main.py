import fastapi
import uvicorn
import logging
import sys
from algo_base.dlog import DLog
from fastapi import status
from fastapi.middleware.cors import CORSMiddleware   
from uvicorn.config import LOG<PERSON>NG_CONFIG   
from contextlib import asynccontextmanager
from api.router import router as api_endpoint_router
from api.health_router import router as health_router
from core.settings import settings
from service.embedding_service import embedding_server
from service.text_extraction_service import text_extraction_service
from utils.common_utils import cost_time
from utils.property_utils import update_property_dict
from utils.health_status import set_server_status
from service.mineru_service import MineruService
from algo_base.custom_consul import ConsulManager

# 初始化 DLog
dlog = DLog()

@cost_time
async def initialize_global_resources():
    """ 初始化全局资源 """
    await set_server_status(status.HTTP_503_SERVICE_UNAVAILABLE)
    embedding_server.initialize()
    MineruService()
    text_extraction_service.initialize()
    ret = await update_property_dict()
    if not ret:
        dlog.errorln("更新属性字典失败")
        raise Exception("更新属性字典失败")

    await set_server_status(status.HTTP_200_OK)


def release_global_resources():
    """ 释放全局资源 """
    pass

 
@asynccontextmanager
async def lifespan(app: fastapi.FastAPI):
    await initialize_global_resources()

    log = DLog()

    #开启consul(可选)
    if  settings.CONSUL_ENABLED:
        await ConsulManager.register_service(
            service_name=settings.SERVICE_NAME,
            service_id_name=settings.SERVICE_NAME,
            service_host=settings.SERVICE_HOST,
            service_port=settings.SERVER_PORT,
            host=settings.CONSUL_HOST,
            port=settings.CONSUL_PORT,
        )
        log.infoln("✅ consul开启成功") 
    yield
    if settings.CONSUL_ENABLED:
        await ConsulManager.deregister_service(
            service_id=settings.SERVICE_NAME,
            host=settings.CONSUL_HOST,
            port=settings.CONSUL_PORT,
    )
    release_global_resources()


def initialize_backend_application() -> fastapi.FastAPI:
    app = fastapi.FastAPI(**settings.set_backend_app_attributes, lifespan=lifespan)  # type: ignore

    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=settings.IS_ALLOWED_CREDENTIALS,
        allow_methods=settings.ALLOWED_METHODS,
        allow_headers=settings.ALLOWED_HEADERS,
    )

    app.include_router(router=api_endpoint_router, prefix=settings.API_PREFIX)
    app.include_router(router=health_router)

    return app


backend_app: fastapi.FastAPI = initialize_backend_application()


def setup_dlog():
    """ 配置 DLog 日志系统
    """
    # 设置 DLog 日志级别
    log_level_name = "debug" if settings.DEBUG else "info"
    log_level = DLog.get_level(log_level_name)
    DLog.set_log(log_level)

    dlog.infof("DLog initialized with level: %s", log_level_name)

    # 使用 DLog 桥接标准 logging
    class DLogHandler(logging.Handler):
        def __init__(self):
            super().__init__()
            self.dlog = DLog()

        def emit(self, record):
            # 过滤掉无用的警告和信息
            message = record.getMessage()

            # 过滤列表
            filtered_messages = [
                "Invalid HTTP request received",
                "Connection broken",
                "Connection lost",
                "Incomplete read",
                "Invalid HTTP method",
                "Invalid HTTP version",
                "Invalid header",
                "changes detected:",  # 过滤文件变动监控信息
                "Change.deleted",     # 过滤文件删除信息
                "Change.modified",    # 过滤文件修改信息
                "Change.added",       # 过滤文件添加信息
                "__pycache__",        # 过滤缓存文件变动
                ".pyc",               # 过滤编译文件变动
                "Property file not found",  # 过滤配置文件未找到警告
                "The current device in use is CPU",  # 过滤MinerU CPU设备警告
                "language is automatically switched",  # 过滤语言自动切换警告
                "GET /health",        # 过滤健康检查请求
                "POST /health",       # 过滤健康检查请求
            ]

            # 检查是否需要过滤
            for filtered_msg in filtered_messages:
                if filtered_msg in message:
                    return

            # 将 logging 日志重定向到 DLog
            if record.levelno >= logging.ERROR:
                self.dlog.errorln(message)
            elif record.levelno >= logging.WARNING:
                self.dlog.warnln(message)
            elif record.levelno >= logging.INFO:
                self.dlog.infoln(message)
            else:
                self.dlog.debugln(message)

    # 设置根日志器
    logging.basicConfig(handlers=[DLogHandler()], level=0)
    for name in logging.root.manager.loggerDict.keys():
        logging.getLogger(name).handlers = [DLogHandler()]

    # 设置特定日志器的级别来减少噪音
    logging.getLogger("uvicorn.protocols.http.httptools_impl").setLevel(logging.ERROR)
    logging.getLogger("uvicorn.protocols.http.h11_impl").setLevel(logging.ERROR)
    logging.getLogger("uvicorn.protocols.websockets.websockets_impl").setLevel(logging.ERROR)
    logging.getLogger("uvicorn.access").setLevel(logging.ERROR)  # 禁用访问日志
    logging.getLogger("httptools").setLevel(logging.ERROR)
    logging.getLogger("websockets").setLevel(logging.ERROR)

    # 设置文件监控相关日志器的级别
    logging.getLogger("watchfiles").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.supervisors.watchfilesreload").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.supervisors").setLevel(logging.WARNING)

    # 替换 Uvicorn 的日志配置
    LOGGING_CONFIG["formatters"]["default"]["fmt"] = "%(levelname)s - %(message)s [%(name)s:%(lineno)d]"

setup_dlog()


if __name__ == "__main__":
    # 将日志级别转换为小写
    log_level = "debug" if settings.DEBUG else "info"

    if settings.DEBUG:
        # 开发模式：启用重载，不使用多进程
        uvicorn.run(
            app="main:backend_app",
            host=settings.SERVER_HOST,
            port=settings.SERVER_PORT,
            reload=True,
            log_level=log_level,
            access_log=False,  # 禁用访问日志
        )
    else:
        # 生产模式：使用多进程，不启用重载
        uvicorn.run(
            app="main:backend_app",
            host=settings.SERVER_HOST,
            port=settings.SERVER_PORT,
            workers=settings.SERVER_WORKERS,
            log_level=log_level,
            access_log=False,  # 禁用访问日志
        )
    