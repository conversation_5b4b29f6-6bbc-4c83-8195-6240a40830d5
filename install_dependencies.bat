@echo off
REM MinerU Service Dependencies Installation Script for Windows
REM This script helps install dependencies for the MinerU service

setlocal enabledelayedexpansion

echo [INFO] MinerU Service Dependencies Installation
echo ========================================

REM Parse command line arguments
set MODE=prod
set PACKAGE_MANAGER=

:parse_args
if "%~1"=="" goto :check_package_manager
if "%~1"=="--dev" (
    set MODE=dev
    shift
    goto :parse_args
)
if "%~1"=="--gpu" (
    set MODE=gpu
    shift
    goto :parse_args
)
if "%~1"=="--uv" (
    set PACKAGE_MANAGER=uv
    shift
    goto :parse_args
)
if "%~1"=="--pip" (
    set PACKAGE_MANAGER=pip
    shift
    goto :parse_args
)
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help

echo [ERROR] Unknown option: %~1
echo Use -h or --help for usage information.
exit /b 1

:show_help
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   --dev     Install development dependencies
echo   --gpu     Install GPU dependencies
echo   --uv      Use UV package manager (recommended)
echo   --pip     Use pip package manager
echo   -h, --help Show this help message
echo.
echo Examples:
echo   %0 --uv           # Install with UV (recommended)
echo   %0 --pip --dev    # Install dev dependencies with pip
echo   %0 --uv --gpu     # Install GPU dependencies with UV
exit /b 0

:check_package_manager
REM Auto-detect package manager if not specified
if "%PACKAGE_MANAGER%"=="" (
    where uv >nul 2>&1
    if !errorlevel! equ 0 (
        set PACKAGE_MANAGER=uv
        echo [INFO] Auto-detected UV package manager
    ) else (
        where pip >nul 2>&1
        if !errorlevel! equ 0 (
            set PACKAGE_MANAGER=pip
            echo [INFO] Auto-detected pip package manager
        ) else (
            echo [ERROR] No package manager found. Please install UV or pip.
            exit /b 1
        )
    )
)

REM Install dependencies
if "%PACKAGE_MANAGER%"=="uv" goto :install_uv
if "%PACKAGE_MANAGER%"=="pip" goto :install_pip

echo [ERROR] Unknown package manager: %PACKAGE_MANAGER%
exit /b 1

:install_uv
echo [INFO] Installing dependencies with UV...

where uv >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] UV is not installed. Please install UV first:
    echo   Visit: https://docs.astral.sh/uv/getting-started/installation/
    exit /b 1
)

REM Initialize UV project if not already done
if not exist "uv.lock" (
    echo [INFO] Initializing UV project...
    uv init --no-readme --no-workspace
)

REM Install dependencies
echo [INFO] Installing production dependencies...
uv sync

if %errorlevel% equ 0 (
    echo [SUCCESS] Dependencies installed successfully with UV!
    echo [INFO] To activate the virtual environment, run: .venv\Scripts\activate
) else (
    echo [ERROR] Failed to install dependencies with UV
    exit /b 1
)
goto :end

:install_pip
echo [INFO] Installing dependencies with pip...

where pip >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] pip is not installed. Please install Python and pip first.
    exit /b 1
)

REM Create virtual environment if it doesn't exist
if not exist ".venv" (
    echo [INFO] Creating virtual environment...
    python -m venv .venv
)

REM Activate virtual environment
echo [INFO] Activating virtual environment...
call .venv\Scripts\activate.bat

REM Upgrade pip
echo [INFO] Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies based on mode
if "%MODE%"=="dev" (
    echo [INFO] Installing development dependencies...
    pip install -r requirements-dev.txt
) else if "%MODE%"=="gpu" (
    echo [INFO] Installing GPU dependencies...
    pip install -r requirements-gpu.txt
) else (
    echo [INFO] Installing production dependencies...
    pip install -r requirements.txt
)

if %errorlevel% equ 0 (
    echo [SUCCESS] Dependencies installed successfully with pip!
    echo [INFO] Virtual environment is activated. To deactivate, run: deactivate
) else (
    echo [ERROR] Failed to install dependencies with pip
    exit /b 1
)

:end
echo [SUCCESS] Installation completed!
echo.
echo [INFO] Next steps:
echo [INFO] 1. Copy .env_dev to .env and configure your settings
echo [INFO] 2. Run the service: python src/main.py
echo [INFO] 3. Visit http://localhost:8081/docs for API documentation

endlocal
