# MinerU Service Makefile
# Provides convenient commands for development and deployment

.PHONY: help install install-dev install-gpu clean test lint format run docker-build docker-run

# Default target
help:
	@echo "MinerU Service - Available Commands:"
	@echo "===================================="
	@echo "  install      Install production dependencies"
	@echo "  install-dev  Install development dependencies"
	@echo "  install-gpu  Install GPU dependencies"
	@echo "  clean        Clean up build artifacts and cache"
	@echo "  test         Run tests"
	@echo "  lint         Run linting checks"
	@echo "  format       Format code with black and isort"
	@echo "  run          Run the service locally"
	@echo "  docker-build Build GPU Docker image"
	@echo "  docker-run   Run GPU Docker container"
	@echo "  switch-gpu   Switch to GPU mode"
	@echo "  switch-cpu   Switch to CPU mode"
	@echo "  show-config  Show current configuration"
	@echo "  help         Show this help message"

# Installation targets
install:
	@echo "Installing production dependencies..."
	@if command -v uv >/dev/null 2>&1; then \
		echo "Using UV..."; \
		uv sync --no-dev; \
	else \
		echo "Using pip..."; \
		pip install -r requirements.txt; \
	fi

install-dev:
	@echo "Installing development dependencies..."
	@if command -v uv >/dev/null 2>&1; then \
		echo "Using UV..."; \
		uv sync; \
	else \
		echo "Using pip..."; \
		pip install -r requirements-dev.txt; \
	fi

install-gpu:
	@echo "Installing GPU dependencies..."
	@if command -v uv >/dev/null 2>&1; then \
		echo "Using UV..."; \
		uv sync --extra gpu; \
	else \
		echo "Using pip..."; \
		pip install -r requirements-gpu.txt; \
	fi

# Cleanup
clean:
	@echo "Cleaning up..."
	@find . -type f -name "*.pyc" -delete
	@find . -type d -name "__pycache__" -delete
	@find . -type d -name "*.egg-info" -exec rm -rf {} +
	@rm -rf build/
	@rm -rf dist/
	@rm -rf .coverage
	@rm -rf htmlcov/
	@rm -rf .pytest_cache/
	@rm -rf .mypy_cache/
	@echo "Cleanup completed!"

# Testing
test:
	@echo "Running tests..."
	@if command -v uv >/dev/null 2>&1; then \
		uv run pytest; \
	else \
		pytest; \
	fi

# Code quality
lint:
	@echo "Running linting checks..."
	@if command -v uv >/dev/null 2>&1; then \
		uv run flake8 src/; \
		uv run mypy src/; \
	else \
		flake8 src/; \
		mypy src/; \
	fi

format:
	@echo "Formatting code..."
	@if command -v uv >/dev/null 2>&1; then \
		uv run black src/; \
		uv run isort src/; \
	else \
		black src/; \
		isort src/; \
	fi

# Development server
run:
	@echo "Starting development server..."
	@if [ ! -f .env ]; then \
		echo "Creating .env from .env_dev..."; \
		cp .env_dev .env; \
	fi
	@if command -v uv >/dev/null 2>&1; then \
		uv run python src/main.py; \
	else \
		python src/main.py; \
	fi

# Docker commands
docker-build:
	@echo "Building GPU Docker image..."
	@./bin/docker-build.sh --type gpu

docker-run:
	@echo "Running GPU Docker container..."
	@./bin/docker-run.sh --type gpu

docker-stop:
	@echo "Stopping Docker container..."
	@docker stop mineru-service 2>/dev/null || true
	@docker rm mineru-service 2>/dev/null || true

docker-compose-up:
	@echo "Starting GPU services with Docker Compose..."
	@docker-compose up -d

docker-compose-down:
	@echo "Stopping Docker Compose services..."
	@docker-compose down

docker-logs:
	@echo "Showing Docker container logs..."
	@docker-compose logs -f

docker-clean:
	@echo "Cleaning Docker images and containers..."
	@docker system prune -f
	@docker images | grep mineru-service | awk '{print $$3}' | xargs -r docker rmi

# Development workflow
dev-setup: install-dev
	@echo "Setting up development environment..."
	@if [ ! -f .env ]; then \
		cp .env_dev .env; \
		echo "Created .env from .env_dev"; \
	fi
	@echo "Development environment ready!"

# CI/CD helpers
ci-test: install-dev lint test
	@echo "CI tests completed!"

# Release helpers
release-check:
	@echo "Checking release readiness..."
	@make lint
	@make test
	@echo "Release checks passed!"
