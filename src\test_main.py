#!/usr/bin/env python3
"""
简化版本的main.py用于测试
"""

from algo_base.dlog import DLog

# 初始化 DLog
dlog = DLog()

dlog.infoln("1. 开始导入基础模块...")
import fastapi
import uvicorn
dlog.infoln("2. 基础模块导入完成")

dlog.infoln("3. 开始导入设置...")
from core.settings import settings
dlog.infof("4. 设置导入完成，端口: %d", settings.SERVER_PORT)

dlog.infoln("5. 开始导入路由...")
from api.router import router as api_endpoint_router
from api.health_router import router as health_router
dlog.infoln("6. 路由导入完成")

dlog.infoln("7. 开始导入服务...")
from service.embedding_service import embedding_server
dlog.infoln("8. embedding_server导入完成")

from service.text_extraction_service import text_extraction_service
dlog.infoln("9. text_extraction_service导入完成")

dlog.infoln("10. 开始创建FastAPI应用...")
app = fastapi.FastAPI(
    debug=settings.DEBUG,
    docs_url=settings.DOCS_URL,
    redoc_url=settings.REDOC_URL,
    root_path=settings.ROOT_PATH,
)
dlog.infoln("11. FastAPI应用创建完成")

dlog.infoln("12. 添加路由...")
app.include_router(router=api_endpoint_router, prefix=settings.API_PREFIX)
app.include_router(router=health_router)
dlog.infoln("13. 路由添加完成")

dlog.infoln("14. 准备启动服务器...")
if __name__ == "__main__":
    dlog.infof("15. 启动服务器在 %s:%d", settings.SERVER_HOST, settings.SERVER_PORT)
    uvicorn.run(
        app=app,
        host=settings.SERVER_HOST,
        port=settings.SERVER_PORT,
        log_level="info",
    )
