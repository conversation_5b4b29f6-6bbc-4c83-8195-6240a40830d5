#!/usr/bin/env python3
"""
测试图像处理修复的脚本
"""

import sys
import os
import tempfile
from pathlib import Path
from PIL import Image
import numpy as np
import io

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.mineru import MineruBase
from algo_base.dlog import DLog

def create_test_image_bytes():
    """创建一个测试图像的字节数据"""
    # 创建一个简单的测试图像
    img = Image.new('RGB', (100, 100), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def create_invalid_bytes():
    """创建无效的字节数据"""
    return b"这不是图像数据"

def test_image_processing():
    """测试图像处理功能"""
    dlog = DLog()
    dlog.infoln("开始测试图像处理修复...")
    
    try:
        # 初始化 MinerU
        mineru = MineruBase()
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            img_folder = Path(temp_dir)
            
            # 测试用例1: 有效的图像字节数据
            dlog.infoln("测试用例1: 有效的图像字节数据")
            valid_image_bytes = create_test_image_bytes()
            images = [valid_image_bytes]
            
            try:
                result = mineru.gen_text_boxes(images, img_folder)
                dlog.infof("测试用例1成功: 返回了 %d 个文本框", len(result))
            except Exception as e:
                dlog.errorf("测试用例1失败: %s", str(e))
            
            # 测试用例2: 无效的字节数据
            dlog.infoln("测试用例2: 无效的字节数据")
            invalid_bytes = create_invalid_bytes()
            images = [invalid_bytes]
            
            try:
                result = mineru.gen_text_boxes(images, img_folder)
                dlog.infof("测试用例2成功: 返回了 %d 个文本框", len(result))
            except Exception as e:
                dlog.errorf("测试用例2失败: %s", str(e))
            
            # 测试用例3: 混合数据类型
            dlog.infoln("测试用例3: 混合数据类型")
            valid_image_bytes = create_test_image_bytes()
            invalid_bytes = create_invalid_bytes()
            images = [valid_image_bytes, invalid_bytes, valid_image_bytes]
            
            try:
                result = mineru.gen_text_boxes(images, img_folder)
                dlog.infof("测试用例3成功: 返回了 %d 个文本框", len(result))
            except Exception as e:
                dlog.errorf("测试用例3失败: %s", str(e))
            
            # 测试用例4: PIL Image 对象
            dlog.infoln("测试用例4: PIL Image 对象")
            pil_image = Image.new('RGB', (200, 200), color='blue')
            images = [pil_image]
            
            try:
                result = mineru.gen_text_boxes(images, img_folder)
                dlog.infof("测试用例4成功: 返回了 %d 个文本框", len(result))
            except Exception as e:
                dlog.errorf("测试用例4失败: %s", str(e))
            
            # 测试用例5: numpy 数组
            dlog.infoln("测试用例5: numpy 数组")
            np_array = np.random.randint(0, 255, (150, 150, 3), dtype=np.uint8)
            images = [np_array]
            
            try:
                result = mineru.gen_text_boxes(images, img_folder)
                dlog.infof("测试用例5成功: 返回了 %d 个文本框", len(result))
            except Exception as e:
                dlog.errorf("测试用例5失败: %s", str(e))
            
            # 测试用例6: 不支持的类型
            dlog.infoln("测试用例6: 不支持的类型")
            unsupported_data = {"not": "an image"}
            images = [unsupported_data]
            
            try:
                result = mineru.gen_text_boxes(images, img_folder)
                dlog.infof("测试用例6成功: 返回了 %d 个文本框", len(result))
            except Exception as e:
                dlog.errorf("测试用例6失败: %s", str(e))
                
    except Exception as e:
        dlog.errorf("测试初始化失败: %s", str(e))
        return False
    
    dlog.infoln("图像处理测试完成!")
    return True

if __name__ == "__main__":
    success = test_image_processing()
    sys.exit(0 if success else 1)
