from utils.type_utils import singleton_func
from service.base_service import BaseService
from algo_base.dlog import DLog
import magic_pdf.model as model_config
from magic_pdf.model.doc_analyze_by_custom_model import (
    ModelSingleton
)


@singleton_func
class MineruService(BaseService):
    def __init__(self):
        self.dlog = DLog()
        self.name = "mineru_service"
        self.mineru_models = None
        self.initialized = False
        super().__init__()

    def initialize(self):
        """初始化服务，加载模型"""
        if self.initialized:
            return

        try:
            # 设置模型配置
            model_config.__use_inside_model__ = True

            # 初始化模型
            mineru_model = ModelSingleton()
            self.mineru_models = mineru_model.get_model(True, False)

            self.initialized = True
        except Exception as e:
            self.dlog.errorf("MineruService初始化失败: %s", str(e))
            import traceback
            self.dlog.errorf("详细错误: %s", traceback.format_exc())
