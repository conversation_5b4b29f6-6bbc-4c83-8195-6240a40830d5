#!/bin/bash

# MinerU Service Dependencies Installation Script
# This script helps install dependencies for the MinerU service

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install with UV (recommended)
install_with_uv() {
    print_info "Installing dependencies with UV..."
    
    if ! command_exists uv; then
        print_error "UV is not installed. Please install UV first:"
        echo "  curl -LsSf https://astral.sh/uv/install.sh | sh"
        echo "  or visit: https://docs.astral.sh/uv/getting-started/installation/"
        exit 1
    fi
    
    # Initialize UV project if not already done
    if [ ! -f "uv.lock" ]; then
        print_info "Initializing UV project..."
        uv init --no-readme --no-workspace
    fi
    
    # Install dependencies
    print_info "Installing production dependencies..."
    uv sync
    
    print_success "Dependencies installed successfully with UV!"
    print_info "To activate the virtual environment, run: source .venv/bin/activate"
}

# Function to install with pip
install_with_pip() {
    print_info "Installing dependencies with pip..."
    
    if ! command_exists pip; then
        print_error "pip is not installed. Please install Python and pip first."
        exit 1
    fi
    
    # Create virtual environment if it doesn't exist
    if [ ! -d ".venv" ]; then
        print_info "Creating virtual environment..."
        python -m venv .venv
    fi
    
    # Activate virtual environment
    print_info "Activating virtual environment..."
    source .venv/bin/activate
    
    # Upgrade pip
    print_info "Upgrading pip..."
    pip install --upgrade pip
    
    # Install dependencies based on mode
    case $1 in
        "dev")
            print_info "Installing development dependencies..."
            pip install -r requirements-dev.txt
            ;;
        "gpu")
            print_info "Installing GPU dependencies..."
            pip install -r requirements-gpu.txt
            ;;
        *)
            print_info "Installing production dependencies..."
            pip install -r requirements.txt
            ;;
    esac
    
    print_success "Dependencies installed successfully with pip!"
    print_info "Virtual environment is activated. To deactivate, run: deactivate"
}

# Main installation logic
main() {
    print_info "MinerU Service Dependencies Installation"
    print_info "========================================"
    
    # Parse command line arguments
    MODE="prod"
    PACKAGE_MANAGER=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dev)
                MODE="dev"
                shift
                ;;
            --gpu)
                MODE="gpu"
                shift
                ;;
            --uv)
                PACKAGE_MANAGER="uv"
                shift
                ;;
            --pip)
                PACKAGE_MANAGER="pip"
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --dev     Install development dependencies"
                echo "  --gpu     Install GPU dependencies"
                echo "  --uv      Use UV package manager (recommended)"
                echo "  --pip     Use pip package manager"
                echo "  -h, --help Show this help message"
                echo ""
                echo "Examples:"
                echo "  $0 --uv           # Install with UV (recommended)"
                echo "  $0 --pip --dev    # Install dev dependencies with pip"
                echo "  $0 --uv --gpu     # Install GPU dependencies with UV"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use -h or --help for usage information."
                exit 1
                ;;
        esac
    done
    
    # Auto-detect package manager if not specified
    if [ -z "$PACKAGE_MANAGER" ]; then
        if command_exists uv; then
            PACKAGE_MANAGER="uv"
            print_info "Auto-detected UV package manager"
        elif command_exists pip; then
            PACKAGE_MANAGER="pip"
            print_info "Auto-detected pip package manager"
        else
            print_error "No package manager found. Please install UV or pip."
            exit 1
        fi
    fi
    
    # Install dependencies
    case $PACKAGE_MANAGER in
        "uv")
            if [ "$MODE" = "dev" ]; then
                print_warning "UV will install dev dependencies automatically based on pyproject.toml"
            elif [ "$MODE" = "gpu" ]; then
                print_warning "For GPU dependencies with UV, use: uv sync --extra gpu"
            fi
            install_with_uv
            ;;
        "pip")
            install_with_pip $MODE
            ;;
        *)
            print_error "Unknown package manager: $PACKAGE_MANAGER"
            exit 1
            ;;
    esac
    
    print_success "Installation completed!"
    print_info ""
    print_info "Next steps:"
    print_info "1. Copy .env_dev to .env and configure your settings"
    print_info "2. Run the service: python src/main.py"
    print_info "3. Visit http://localhost:8081/docs for API documentation"
}

# Run main function
main "$@"
