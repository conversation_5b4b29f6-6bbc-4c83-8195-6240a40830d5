#!/bin/bash

# Docker运行脚本
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

cd "$PROJECT_DIR"

# 默认参数
RUN_TYPE="cpu"
VERSION="1.3.10"
PORT="8081"
DETACH=true
REMOVE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --type)
            RUN_TYPE="$2"
            shift 2
            ;;
        --version)
            VERSION="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --foreground)
            DETACH=false
            shift
            ;;
        --rm)
            REMOVE=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --type TYPE      运行类型 (cpu|gpu) [默认: cpu]"
            echo "  --version VER    版本号 [默认: 1.3.10]"
            echo "  --port PORT      端口号 [默认: 8081]"
            echo "  --foreground     前台运行 (不使用 -d)"
            echo "  --rm             容器停止后自动删除"
            echo "  -h, --help       显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 镜像名称
IMAGE_NAME="mineru-service"
CONTAINER_NAME="mineru-service-$RUN_TYPE"
IMAGE_TAG="$IMAGE_NAME:$VERSION-$RUN_TYPE"

echo "🚀 启动 MinerU Service Docker 容器..."
echo "📋 运行类型: $RUN_TYPE"
echo "📋 镜像标签: $IMAGE_TAG"
echo "📋 容器名称: $CONTAINER_NAME"
echo "📋 端口映射: $PORT:8081"

# 检查镜像是否存在
if ! docker images | grep -q "$IMAGE_NAME.*$VERSION-$RUN_TYPE"; then
    echo "❌ 镜像不存在: $IMAGE_TAG"
    echo "请先运行构建脚本: ./bin/docker-build.sh --type $RUN_TYPE"
    exit 1
fi

# 停止并删除已存在的容器
if docker ps -a | grep -q "$CONTAINER_NAME"; then
    echo "🛑 停止已存在的容器: $CONTAINER_NAME"
    docker stop "$CONTAINER_NAME" >/dev/null 2>&1 || true
    docker rm "$CONTAINER_NAME" >/dev/null 2>&1 || true
fi

# 创建数据目录
mkdir -p "$PROJECT_DIR/data"

# 构建docker run命令
DOCKER_CMD="docker run"

# 添加运行选项
if [ "$DETACH" = true ]; then
    DOCKER_CMD="$DOCKER_CMD -d"
fi

if [ "$REMOVE" = true ]; then
    DOCKER_CMD="$DOCKER_CMD --rm"
fi

# 添加基本参数
DOCKER_CMD="$DOCKER_CMD --name $CONTAINER_NAME"
DOCKER_CMD="$DOCKER_CMD -p $PORT:8081"

# 添加卷挂载
DOCKER_CMD="$DOCKER_CMD -v $PROJECT_DIR/data:/app/data"
DOCKER_CMD="$DOCKER_CMD -v $PROJECT_DIR/config:/app/config"
DOCKER_CMD="$DOCKER_CMD -v $PROJECT_DIR/confs:/app/confs"

# 添加环境变量
DOCKER_CMD="$DOCKER_CMD -e DEBUG=false"
DOCKER_CMD="$DOCKER_CMD -e SERVER_HOST=0.0.0.0"
DOCKER_CMD="$DOCKER_CMD -e SERVER_PORT=8081"

# GPU特定配置
if [ "$RUN_TYPE" = "gpu" ]; then
    DOCKER_CMD="$DOCKER_CMD --gpus all"
    DOCKER_CMD="$DOCKER_CMD -e DEVICE=cuda"
    DOCKER_CMD="$DOCKER_CMD -e CUDA_VISIBLE_DEVICES=0"
    DOCKER_CMD="$DOCKER_CMD -e SERVER_WORKERS=2"
else
    DOCKER_CMD="$DOCKER_CMD -e DEVICE=cpu"
    DOCKER_CMD="$DOCKER_CMD -e SERVER_WORKERS=4"
fi

# 添加镜像名称
DOCKER_CMD="$DOCKER_CMD $IMAGE_TAG"

echo "🔧 执行命令: $DOCKER_CMD"
echo ""

# 执行命令
eval $DOCKER_CMD

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功!"
    echo ""
    echo "📋 容器信息:"
    docker ps | grep "$CONTAINER_NAME"
    echo ""
    echo "🌐 服务地址: http://localhost:$PORT"
    echo "📖 API文档: http://localhost:$PORT/docs"
    echo "🔍 健康检查: http://localhost:$PORT/health"
    echo ""
    echo "📝 查看日志: docker logs -f $CONTAINER_NAME"
    echo "🛑 停止容器: docker stop $CONTAINER_NAME"
else
    echo "❌ 容器启动失败!"
    exit 1
fi
