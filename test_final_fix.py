#!/usr/bin/env python3
"""
测试最终修复的脚本
"""

import sys
import os
import tempfile
from pathlib import Path
from PIL import Image
import io
import asyncio

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from service.text_extraction_service import text_extraction_service
from fastapi import UploadFile
from io import BytesIO

class MockUploadFile:
    """模拟 FastAPI 的 UploadFile"""
    def __init__(self, content: bytes, filename: str, content_type: str):
        self.content = content
        self.filename = filename
        self.content_type = content_type
        self._file = BytesIO(content)
    
    async def read(self) -> bytes:
        return self.content

def create_simple_pdf_bytes():
    """创建一个简单的PDF文件字节数据"""
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Hello World) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
    return pdf_content

def create_test_image_bytes():
    """创建一个测试图像的字节数据"""
    img = Image.new('RGB', (200, 200), color='blue')
    # 添加一些文本区域
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    draw.rectangle([20, 20, 180, 60], fill='white', outline='black')
    draw.rectangle([20, 80, 180, 120], fill='white', outline='black')
    
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

async def test_pdf_processing():
    """测试PDF文件处理"""
    print("测试PDF文件处理...")
    
    try:
        # 初始化服务
        text_extraction_service.initialize()
        
        # 创建测试PDF
        pdf_bytes = create_simple_pdf_bytes()
        mock_file = MockUploadFile(pdf_bytes, "test.pdf", "application/pdf")
        
        # 调用服务
        response = await text_extraction_service.extract_text_boxes_from_files([mock_file])
        
        print(f"PDF处理结果:")
        print(f"  处理时间: {response.processing_time:.2f}秒")
        print(f"  文本框数量: {response.total_count}")
        print(f"  错误信息: {response.error_msg}")
        
        # 检查文本框数据
        if response.text_boxes:
            for i, text_box in enumerate(response.text_boxes):
                print(f"  文本框 {i+1}:")
                print(f"    文本: '{text_box.text}'")
                print(f"    类型: {text_box.type}")
                print(f"    页面尺寸: {text_box.page_size}")
                print(f"    页面尺寸类型: {type(text_box.page_size)}")
                if text_box.page_size:
                    print(f"    宽度类型: {type(text_box.page_size[0])}, 高度类型: {type(text_box.page_size[1])}")
        
        return response.error_msg is None
        
    except Exception as e:
        print(f"PDF处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_image_processing():
    """测试图像文件处理"""
    print("测试图像文件处理...")
    
    try:
        # 创建测试图像
        image_bytes = create_test_image_bytes()
        mock_file = MockUploadFile(image_bytes, "test.png", "image/png")
        
        # 调用服务
        response = await text_extraction_service.extract_text_boxes_from_files([mock_file])
        
        print(f"图像处理结果:")
        print(f"  处理时间: {response.processing_time:.2f}秒")
        print(f"  文本框数量: {response.total_count}")
        print(f"  错误信息: {response.error_msg}")
        
        # 检查文本框数据
        if response.text_boxes:
            for i, text_box in enumerate(response.text_boxes):
                print(f"  文本框 {i+1}:")
                print(f"    文本: '{text_box.text}'")
                print(f"    类型: {text_box.type}")
                print(f"    页面尺寸: {text_box.page_size}")
                if text_box.page_size:
                    print(f"    宽度类型: {type(text_box.page_size[0])}, 高度类型: {type(text_box.page_size[1])}")
        
        return response.error_msg is None
        
    except Exception as e:
        print(f"图像处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("开始测试最终修复...")
    print("=" * 60)
    
    # PDF处理测试
    test1 = await test_pdf_processing()
    print("-" * 60)
    
    # 图像处理测试
    test2 = await test_image_processing()
    print("-" * 60)
    
    if test1 and test2:
        print("✅ 所有测试通过!")
        print("✅ PIL.UnidentifiedImageError 已修复")
        print("✅ page_size 浮点数问题已修复")
        return True
    else:
        print("❌ 部分测试失败!")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
