#!/usr/bin/env python3
"""
MinerU Service 设备模式切换工具
快速在 CPU 和 GPU 模式之间切换配置
"""

import argparse
import os
import sys
from pathlib import Path
from src.algo_base.dlog import DLog

# 初始化 DLog
dlog = DLog()


def update_env_file(device_mode: str, debug: bool = None):
    """
    更新 .env 文件中的设备配置

    Args:
        device_mode: 设备模式 ('cpu' 或 'cuda')
        debug: 是否启用调试模式
    """
    env_file = Path(".env")

    if not env_file.exists():
        dlog.errorln("❌ .env 文件不存在，请先复制 .env.example 到 .env")
        return False

    # 读取现有配置
    lines = []
    with open(env_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # 更新配置
    updated_lines = []
    for line in lines:
        line = line.strip()

        if line.startswith('DEVICE='):
            updated_lines.append(f'DEVICE={device_mode}\n')
        elif line.startswith('SERVER_WORKERS='):
            # 根据设备类型调整工作进程数
            workers = 2 if device_mode == 'cuda' else 4
            updated_lines.append(f'SERVER_WORKERS={workers}\n')
        elif line.startswith('DEBUG=') and debug is not None:
            updated_lines.append(f'DEBUG={str(debug).lower()}\n')
        else:
            updated_lines.append(line + '\n' if not line.endswith('\n') else line + '\n')

    # 写回文件
    with open(env_file, 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)

    return True


def show_current_config():
    """显示当前配置"""
    env_file = Path(".env")
    
    if not env_file.exists():
        dlog.errorln("❌ .env 文件不存在")
        return

    dlog.infoln("📋 当前配置:")
    dlog.infoln("=" * 50)

    with open(env_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line.startswith(('DEVICE=', 'SERVER_WORKERS=', 'DEBUG=', 'MODE=', 'CONSUL_ENABLED=', 'SERVICE_NAME=')):
                dlog.infof("  %s", line)

    dlog.infoln("=" * 50)


def main():
    parser = argparse.ArgumentParser(
        description="MinerU Service 设备模式切换工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python switch_device.py --gpu              # 切换到GPU模式
  python switch_device.py --cpu              # 切换到CPU模式
  python switch_device.py --gpu --debug      # 切换到GPU模式并启用调试
  python switch_device.py --show             # 显示当前配置
        """
    )
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--cpu', action='store_true', help='切换到CPU模式')
    group.add_argument('--gpu', action='store_true', help='切换到GPU模式')
    group.add_argument('--show', action='store_true', help='显示当前配置')
    
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--no-debug', action='store_true', help='禁用调试模式')
    
    args = parser.parse_args()
    
    if args.show:
        show_current_config()
        return
    
    # 确定设备模式
    device_mode = 'cuda' if args.gpu else 'cpu'
    
    # 确定调试模式
    debug_mode = None
    if args.debug:
        debug_mode = True
    elif args.no_debug:
        debug_mode = False
    
    dlog.infof("🔄 切换到 %s 模式...", device_mode.upper())

    if update_env_file(device_mode, debug_mode):
        dlog.infof("✅ 成功切换到 %s 模式!", device_mode.upper())

        if device_mode == 'cuda':
            dlog.infoln("\n📝 GPU 模式配置说明:")
            dlog.infoln("  - 工作进程数: 2 (避免GPU内存冲突)")
            dlog.infoln("  - 需要 NVIDIA GPU 和 CUDA 支持")
            dlog.infoln("  - 确保已安装 GPU 版本的依赖")
        else:
            dlog.infoln("\n📝 CPU 模式配置说明:")
            dlog.infoln("  - 工作进程数: 4 (充分利用CPU)")
            dlog.infoln("  - 适合开发和轻量级部署")

        dlog.infoln("\n🚀 重启服务以应用新配置:")
        dlog.infoln("  docker-compose down && docker-compose up -d")
        dlog.infoln("  或")
        dlog.infoln("  python src/main.py")

        dlog.infoln("\n📋 当前配置:")
        show_current_config()
    else:
        dlog.errorln("❌ 配置更新失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
