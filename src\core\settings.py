from dotenv import find_dotenv
from pydantic import (
    HttpUrl,
    SecretStr,
    TypeAdapter,
    computed_field,
    field_validator,
)
from pydantic_settings import BaseSettings, SettingsConfigDict
import os


def check_str_is_http(x: str) -> str:
    http_url_adapter = TypeAdapter(HttpUrl)
    return str(http_url_adapter.validate_python(x))


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=find_dotenv(),
        env_file_encoding="utf-8",
        env_ignore_empty=True,
        extra="ignore",
        validate_default=False,
    )

    # 应用模式和调试
    MODE: str = "prod"
    DEBUG: bool = False

    # API 文档配置
    DOCS_URL: str | None = "/docs"
    REDOC_URL: str | None = "/redoc"
    ROOT_PATH: str | None = "/openapi"
    API_PREFIX: str | None = "/api"

    # 服务器配置
    SERVER_HOST: str = "0.0.0.0"
    SERVER_PORT: int = 8081
    SERVER_WORKERS: int = 2

    # 设备配置
    DEVICE: str = "cuda"

    # CORS 配置
    ALLOWED_ORIGINS: list[str] = ["*"]
    IS_ALLOWED_CREDENTIALS: bool = True
    ALLOWED_METHODS: list[str] = ["*"]
    ALLOWED_HEADERS: list[str] = ["*"]

    # Consul 配置
    CONSUL_ENABLED: bool = False
    CONSUL_HOST: str = "consul.dcx.com"
    CONSUL_PORT: int = 8500
    SERVICE_NAME: str = "mineru-service-1.3.10"
    SERVICE_HOST: str = "localhost"
    MINERU_SERVICE_NAME: str = "mineru-service-1.3.10"



    @computed_field  # type: ignore[prop-decorator]
    @property
    def BASE_URL(self) -> str:
        return f"http://{self.SERVER_HOST}:{self.SERVER_PORT}"

    def is_dev(self) -> bool:
        return self.MODE == "dev"

    def is_gpu_mode(self) -> bool:
        """检查是否为GPU模式"""
        return self.DEVICE.lower() in ["cuda", "gpu"]

    def is_cpu_mode(self) -> bool:
        """检查是否为CPU模式"""
        return self.DEVICE.lower() == "cpu"

    @computed_field
    @property
    def optimized_workers(self) -> int:
        """根据设备类型自动优化工作进程数"""
        if self.is_gpu_mode():
            # GPU模式使用较少进程避免显存冲突
            return min(self.SERVER_WORKERS, 2)
        else:
            # CPU模式可以使用更多进程
            return max(self.SERVER_WORKERS, 4)

    @computed_field
    @property
    def model_device(self) -> str:
        """获取模型设备类型"""
        return "cuda" if self.is_gpu_mode() else "cpu"

    @property
    def set_backend_app_attributes(self) -> dict:
        return {
            "debug": self.DEBUG,
            "docs_url": self.DOCS_URL,
            "redoc_url": self.REDOC_URL,
            "root_path": self.ROOT_PATH,
            "api_prefix": self.API_PREFIX,
        }

settings = Settings()
