#!/usr/bin/env python3
"""
测试服务修复的脚本
"""

import sys
import os
import tempfile
from pathlib import Path
from PIL import Image
import io
import asyncio

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from service.text_extraction_service import text_extraction_service
from fastapi import UploadFile
from io import BytesIO

class MockUploadFile:
    """模拟 FastAPI 的 UploadFile"""
    def __init__(self, content: bytes, filename: str, content_type: str):
        self.content = content
        self.filename = filename
        self.content_type = content_type
        self._file = BytesIO(content)
    
    async def read(self) -> bytes:
        return self.content

def create_test_image_bytes():
    """创建一个测试图像的字节数据"""
    img = Image.new('RGB', (200, 200), color='blue')
    # 添加一些文本区域（简单的矩形）
    from PIL import ImageDraw, ImageFont
    draw = ImageDraw.Draw(img)
    
    # 绘制一些矩形作为文本区域
    draw.rectangle([20, 20, 180, 60], fill='white', outline='black')
    draw.rectangle([20, 80, 180, 120], fill='white', outline='black')
    draw.rectangle([20, 140, 180, 180], fill='white', outline='black')
    
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def create_invalid_image_bytes():
    """创建无效的图像字节数据"""
    return b"This is not a valid image file content"

async def test_service_with_valid_image():
    """测试服务处理有效图像"""
    print("测试服务处理有效图像...")
    
    try:
        # 初始化服务
        text_extraction_service.initialize()
        
        # 创建测试图像
        image_bytes = create_test_image_bytes()
        mock_file = MockUploadFile(image_bytes, "test.png", "image/png")
        
        # 调用服务
        response = await text_extraction_service.extract_text_boxes_from_files([mock_file])
        
        print(f"服务调用成功!")
        print(f"处理时间: {response.processing_time:.2f}秒")
        print(f"文本框数量: {response.total_count}")
        print(f"错误信息: {response.error_msg}")
        
        return response.error_msg is None
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

async def test_service_with_invalid_image():
    """测试服务处理无效图像"""
    print("测试服务处理无效图像...")
    
    try:
        # 创建无效图像数据
        invalid_bytes = create_invalid_image_bytes()
        mock_file = MockUploadFile(invalid_bytes, "invalid.png", "image/png")
        
        # 调用服务
        response = await text_extraction_service.extract_text_boxes_from_files([mock_file])
        
        print(f"服务调用完成")
        print(f"处理时间: {response.processing_time:.2f}秒")
        print(f"文本框数量: {response.total_count}")
        print(f"错误信息: {response.error_msg}")
        
        # 对于无效图像，我们期望服务能够优雅地处理而不是崩溃
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

async def test_service_with_mixed_images():
    """测试服务处理混合图像（有效和无效）"""
    print("测试服务处理混合图像...")
    
    try:
        # 创建混合图像数据
        valid_bytes = create_test_image_bytes()
        invalid_bytes = create_invalid_image_bytes()
        
        mock_files = [
            MockUploadFile(valid_bytes, "valid.png", "image/png"),
            MockUploadFile(invalid_bytes, "invalid.png", "image/png"),
            MockUploadFile(valid_bytes, "valid2.png", "image/png")
        ]
        
        # 调用服务
        response = await text_extraction_service.extract_text_boxes_from_files(mock_files)
        
        print(f"服务调用完成")
        print(f"处理时间: {response.processing_time:.2f}秒")
        print(f"文本框数量: {response.total_count}")
        print(f"错误信息: {response.error_msg}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始测试服务修复...")
    
    test1 = await test_service_with_valid_image()
    print("-" * 50)
    
    test2 = await test_service_with_invalid_image()
    print("-" * 50)
    
    test3 = await test_service_with_mixed_images()
    print("-" * 50)
    
    if test1 and test2 and test3:
        print("所有测试通过!")
        return True
    else:
        print("部分测试失败!")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
