from fastapi import APIRouter, HTTPException, File, UploadFile
from typing import List
from algo_base.dlog import DLog

from service.text_extraction_service import text_extraction_service

router = APIRouter(prefix="/text-extraction", tags=["文本提取相关接口"])

# 初始化 DLog
dlog = DLog()


@router.post(
        "/extract-text-boxes", 
        summary="提取图像文本框",
        description="上传一张或多张图像文件，基于视觉分割生成文本框列表。"
)
async def extract_text_boxes(
    images: List[UploadFile] = File(..., description="需要处理的图像文件列表"),
):
    try:
        # 验证请求参数
        if not images:
            raise HTTPException(
                status_code=400,
                detail="图片文件列表不能为空"
            )

        if len(images) > 50:  # 限制最大图片数量
            raise HTTPException(
                status_code=400,
                detail="图片数量不能超过50张"
            )

        # 验证文件类型
        allowed_types = {'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'}
        for image in images:
            if image.content_type not in allowed_types:
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的文件类型: {image.content_type}，支持的类型: {', '.join(allowed_types)}"
                )


        # 调用服务进行处理
        response = await text_extraction_service.extract_text_boxes_from_files(images)

        # 检查是否有错误
        if response.error_msg:
            dlog.errorf("文本框提取失败: %s", response.error_msg)
            raise HTTPException(
                status_code=500,
                detail=f"文本框提取失败: {response.error_msg}"
            )

        return response
        
    except HTTPException:
        # 重新抛出 HTTP 异常
        raise
    except Exception as e:
        dlog.errorf("文本框提取接口异常: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误: {str(e)}"
        )

