#!/usr/bin/env python3
"""
测试当前代码状态
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_code_content():
    """检查代码内容"""
    print("检查 mineru.py 文件内容...")
    
    mineru_path = os.path.join(os.path.dirname(__file__), 'src', 'core', 'mineru.py')
    
    if not os.path.exists(mineru_path):
        print(f"❌ 文件不存在: {mineru_path}")
        return False
    
    with open(mineru_path, 'r', encoding='utf-8') as f:
        content = f.read()

    lines = content.split('\n')

    # 检查是否还有旧的问题代码
    if 'images[page_idx]' in content:
        print("❌ 发现旧的问题代码 'images[page_idx]'")

        # 找到所有出现的位置
        for i, line in enumerate(lines, 1):
            if 'images[page_idx]' in line:
                print(f"  第 {i} 行: {line.strip()}")
        return False
    else:
        print("✅ 没有发现 'images[page_idx]' 问题代码")

    # 检查是否有PDF验证逻辑
    if 'image_bytes.startswith(b\'%PDF\')' in content:
        print("✅ 发现PDF验证逻辑")
    else:
        print("❌ 没有发现PDF验证逻辑")
        return False

    # 检查是否有page_size转换逻辑
    if 'int(round(text_box[\'page_size\'][0]))' in content:
        print("✅ 发现page_size转换逻辑")
    else:
        print("❌ 没有发现page_size转换逻辑")
        return False

    print(f"✅ 文件总行数: {len(lines)}")
    return True

def test_import():
    """测试导入"""
    print("测试模块导入...")
    
    try:
        from core.mineru import MineruBase
        print("✅ 成功导入 MineruBase")
        
        # 检查方法是否存在
        mineru = MineruBase()
        if hasattr(mineru, 'gen_text_boxes'):
            print("✅ gen_text_boxes 方法存在")
        else:
            print("❌ gen_text_boxes 方法不存在")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始检查当前代码状态...")
    print("=" * 50)
    
    test1 = test_code_content()
    print("-" * 50)
    
    test2 = test_import()
    print("-" * 50)
    
    if test1 and test2:
        print("✅ 代码状态检查通过")
        return True
    else:
        print("❌ 代码状态检查失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
