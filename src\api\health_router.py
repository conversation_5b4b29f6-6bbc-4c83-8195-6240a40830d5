from fastapi.responses import JSONResponse
from fastapi import APIRouter, status, HTTPException 
from utils.health_status import get_server_status, set_server_status


router = APIRouter(prefix="/health", tags=["健康检查相关接口"])


@router.get("/")
async def get_healthy():
    """
    获取健康状态
    """
    code = await get_server_status()
    return JSONResponse(status_code=code, content={"status": "ok"})


@router.get("/get")
async def get_health_status():
    """
    获取健康检查状态
    """
    if await get_server_status() != status.HTTP_200_OK:
        raise HTTPException(status_code=await get_server_status(), detail="Service is not available")
    return {"status": "ok"}


@router.get("/set")
async def set_health_status(
    code: int,
) -> dict:
    """
    设置健康检查状态
    
    Args:
        code: 健康检查状态
    """
    await set_server_status(code)
    return {"status": "ok"}
