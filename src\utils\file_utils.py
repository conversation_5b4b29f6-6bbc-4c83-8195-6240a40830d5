import hashlib
import httpx

def calculate_file_hash(file, algorithm='md5'):
    """ 计算给定文件的哈希值
    
    Args:
        file: 文件对象
        algorithm: 哈希算法的名字，可选'sha256', 'md5'
    Returns:
        文件的哈希值
    """
    with open(file, 'rb') as f:
        file_bytes = f.read()
    return calculate_bytes_hash(file_bytes, algorithm)


def calculate_bytes_hash(file_bytes, algorithm='md5'):
    """ 计算给定文件的哈希值
    
    Args:
        file_bytes: 文件字节流
        algorithm: 哈希算法的名字，可选'sha256', 'md5'
    Returns:
        文件的哈希值
    """
    if algorithm == 'sha256':
        hasher = hashlib.sha256()
        # 在Python3中，需要将字符串转化为字节对象才能被哈希函数处理
        #input_bytes = file_bytes.encode('utf-8')
        hasher.update(file_bytes)
        return hasher.hexdigest()
    elif algorithm == 'md5':
        hasher = hashlib.md5()
        hasher.update(file_bytes)
        return hasher.hexdigest().upper()
    else:
        raise ValueError(f"Unsupported algorithm: {algorithm}")


async def download_image(url: str) -> bytes:
    """ 下载文件（异步版本）
    
    Args:
        url: 文件URL
        
    Returns:
        文件字节流
    """
    async with httpx.AsyncClient() as client:
        response = await client.get(url)
        return response.content


def download_image_sync(url: str) -> bytes:
    """ 下载文件（同步版本）
    
    Args:
        url: 文件URL
        
    Returns:
        文件字节流
    """
    from algo_base.dlog import DLog
    dlog = DLog()
    try:
        with httpx.Client() as client:
            response = client.get(url)
            if response.status_code == 200:
                return response.content
            else:
                dlog.errorf("下载图片失败，状态码: %d, URL: %s", response.status_code, url)
                return None
    except Exception as e:
        dlog.errorf("下载图片时发生异常: %s, URL: %s", str(e), url)
        return None
