name: mineru-1.3.10

services:
  mineru-service:
    build:
      context: .
      dockerfile: deploy/Dockerfile-gpu
      args:
        VERSION: "1.3.10"
    image: mineru-service-1.3.10:latest
    container_name: mineru-service-1.3.10
    ports:
      - "8081:8081"
    environment:
      - DEBUG=false
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8081
      - SERVER_WORKERS=2
      - DEVICE=cuda
      - CUDA_VISIBLE_DEVICES=0
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - ./confs:/app/confs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - mineru-network

networks:
  mineru-network:
    driver: bridge

volumes:
  mineru-data:
    driver: local
