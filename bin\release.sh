#!/bin/bash

DOCKERFILE=$1
VERSION=$2

WORKDIR=$(cd $(dirname $0)/..; pwd)
echo "WORKDIR: $WORKDIR"
cd $WORKDIR

MODULE_NAME=fastapi-server-template
HARBOR_ADDRESS=XXXX
USER=XXXX
PASSWORD=XXXX
IMAGE_TAG_NAME=$MODULE_NAME:v$VERSION
DOCKER_REPOSITORY=$HARBOR_ADDRESS/fastapi-server-template/$IMAGE_TAG_NAME

echo IMAGE_TAG_NAME: $IMAGE_TAG_NAME
echo DOCKER_REPOSITORY: $DOCKER_REPOSITORY

echo "docker build -t $DOCKER_REPOSITORY -f $DOCKERFILE $WORKDIR"
if ! docker build -t $DOCKER_REPOSITORY -f $DOCKERFILE $WORKDIR; then
  echo "docker build fails"
  exit 1
fi

# echo "git tag v${VERSION}"
# if ! git tag -a "v${VERSION}"; then
#   echo "git tag v${VERSION} fails"
#   exit 1
# fi

# echo "git push tag v${VERSION}"
# if ! git push origin "v${VERSION}"; then
#   echo "git push tag v${VERSION} fails"
#   exit 1
# fi


echo "docker push ${DOCKER_REPOSITORY}"
if ! docker push ${DOCKER_REPOSITORY}; then
  echo "docker push fails"
  exit 1
fi

echo "image $MODULE_NAME build success."

