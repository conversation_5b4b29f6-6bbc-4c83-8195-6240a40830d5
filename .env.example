# MinerU Service Configuration
# 统一配置文件 - 支持 CPU 和 GPU 模式切换

# =============================================================================
# 核心配置 - 根据需要修改这里
# =============================================================================

# 设备模式选择: cpu 或 cuda
DEVICE=cuda

# 运行模式: dev, prod
MODE=prod

# =============================================================================
# 服务器配置
# =============================================================================

# 调试模式
DEBUG=False

# 服务器地址和端口
SERVER_HOST=0.0.0.0
SERVER_PORT=8081

# 工作进程数 (GPU模式建议1-2个，CPU模式建议4个)
SERVER_WORKERS=2

# =============================================================================
# CORS 配置
# =============================================================================

IS_ALLOWED_CREDENTIALS=True
ALLOWED_ORIGINS=["*"]
ALLOWED_METHODS=["*"]
ALLOWED_HEADERS=["*"]

# =============================================================================
# API 文档配置
# =============================================================================

DOCS_URL=/docs
REDOC_URL=/redoc
ROOT_PATH=/openapi
API_PREFIX=/api

# =============================================================================
# Consul 服务发现配置
# =============================================================================

# 是否启用 Consul 服务发现
CONSUL_ENABLED=true

# Consul 服务器配置
CONSUL_HOST=consul.dcx.com
CONSUL_PORT=8500

# 服务注册配置
SERVICE_NAME=mineru-service-1.3.10
SERVICE_HOST="************"
MINERU_SERVICE_NAME=mineru-service-1.3.10