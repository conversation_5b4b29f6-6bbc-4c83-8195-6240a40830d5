from enum import Enum
from pydantic import BaseModel
from typing import List


class QueryType(Enum):
    QUERY = "query"
    ITEM = "item"


class Request(BaseModel):
    request_id: str | None = None  # 请求ID
    text: str | None = None  # 文本，ITEM的文本描述或者QUERY中的summary
    image_url: str | None = None  # ITEM的图像URL
    query_type: QueryType | None = None  # 查询类型，QUERY或者ITEM
    quality_factors: str | None = None  # 质量因子，用逗号分隔，例如："1.0,2.0,3.0"
    id: str | None = None  # 用户|ITEM的ID


class Response(BaseModel):
    request_id: str | None = None  # 请求ID
    embedding: list[float] | None = None  # 识别的embedding
    error_msg: str | None = None  # 错误信息



class TextBox(BaseModel):
    """文本框信息"""
    page_idx: int  # 页面索引
    bbox: List[int] | None = None  # 边界框坐标 [x1, y1, x2, y2]
    text: str | None = None  # 提取的文本内容
    type: str | None = None  # 文本框类型（如：title, paragraph, table等）
    page_size: List[int] | None = None  # 页面尺寸 [width, height]


class TextBoxResponse(BaseModel):
    text_boxes: List[TextBox]  # 文本框列表
    total_count: int  # 文本框总数
    processing_time: float | None = None  # 处理时间（秒）
    error_msg: str | None = None  # 错误信息

