#!/usr/bin/env python3
"""
测试PDF转PNG图像处理
"""

import sys
import os
import tempfile
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import io
import asyncio

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from service.text_extraction_service import text_extraction_service
from fastapi import UploadFile
from io import BytesIO

class MockUploadFile:
    """模拟 FastAPI 的 UploadFile"""
    def __init__(self, content: bytes, filename: str, content_type: str):
        self.content = content
        self.filename = filename
        self.content_type = content_type
        self._file = BytesIO(content)
    
    async def read(self) -> bytes:
        return self.content

def create_pdf_like_png_image():
    """创建一个模拟PDF页面的PNG图像"""
    # 使用PDF标准页面尺寸 (612x792 points, 约等于 8.5x11 inches at 72 DPI)
    width, height = 612, 792
    
    # 创建白色背景的图像
    img = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(img)
    
    # 尝试使用系统字体，如果失败则使用默认字体
    try:
        # Windows系统字体
        font_title = ImageFont.truetype("arial.ttf", 24)
        font_text = ImageFont.truetype("arial.ttf", 12)
    except:
        try:
            # 备用字体
            font_title = ImageFont.load_default()
            font_text = ImageFont.load_default()
        except:
            font_title = None
            font_text = None
    
    # 绘制标题
    title_text = "15自相矛盾"
    if font_title:
        draw.text((50, 50), title_text, fill='black', font=font_title)
    else:
        draw.text((50, 50), title_text, fill='black')
    
    # 绘制正文段落
    paragraphs = [
        "这是第一段文本内容，模拟PDF页面中的正文。",
        "这是第二段文本内容，包含更多的文字信息。",
        "这是第三段文本内容，用于测试文本框提取功能。"
    ]
    
    y_pos = 120
    for para in paragraphs:
        if font_text:
            draw.text((50, y_pos), para, fill='black', font=font_text)
        else:
            draw.text((50, y_pos), para, fill='black')
        y_pos += 40
    
    # 绘制一个表格框架
    table_x, table_y = 50, 300
    table_width, table_height = 400, 150
    
    # 表格边框
    draw.rectangle([table_x, table_y, table_x + table_width, table_y + table_height], 
                   outline='black', width=2)
    
    # 表格内部分割线
    for i in range(1, 3):  # 2行
        y = table_y + (table_height // 3) * i
        draw.line([table_x, y, table_x + table_width, y], fill='black', width=1)
    
    for i in range(1, 4):  # 3列
        x = table_x + (table_width // 4) * i
        draw.line([x, table_y, x, table_y + table_height], fill='black', width=1)
    
    # 在表格中添加文本
    cell_texts = ["标题1", "标题2", "标题3", "数据1", "数据2", "数据3"]
    for i, text in enumerate(cell_texts):
        row = i // 3
        col = i % 3
        x = table_x + (table_width // 4) * col + 10
        y = table_y + (table_height // 3) * row + 10
        if font_text:
            draw.text((x, y), text, fill='black', font=font_text)
        else:
            draw.text((x, y), text, fill='black')
    
    # 添加页码（模拟PDF页面的页码）
    page_num = "1"
    if font_text:
        # 获取文本尺寸
        bbox = draw.textbbox((0, 0), page_num, font=font_text)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
    else:
        text_width, text_height = 10, 10
    
    # 页码居中底部
    page_x = (width - text_width) // 2
    page_y = height - 50
    
    if font_text:
        draw.text((page_x, page_y), page_num, fill='black', font=font_text)
    else:
        draw.text((page_x, page_y), page_num, fill='black')
    
    # 转换为字节数据
    buffer = io.BytesIO()
    img.save(buffer, format='PNG', dpi=(72, 72))  # 设置DPI为72，匹配PDF标准
    return buffer.getvalue()

def create_corrupted_png_bytes():
    """创建一个损坏的PNG字节数据（模拟转换过程中的问题）"""
    # 创建正常的PNG
    normal_png = create_pdf_like_png_image()
    
    # 故意损坏一些字节（但保持PNG头部完整）
    corrupted = bytearray(normal_png)
    
    # 损坏中间的一些字节
    if len(corrupted) > 100:
        for i in range(50, 60):
            corrupted[i] = 0xFF
    
    return bytes(corrupted)

async def test_pdf_to_png_processing():
    """测试PDF转PNG图像处理"""
    print("测试PDF转PNG图像处理...")
    
    try:
        # 清理Python缓存（确保使用最新代码）
        import importlib
        import sys
        if 'service.text_extraction_service' in sys.modules:
            importlib.reload(sys.modules['service.text_extraction_service'])
        if 'core.mineru' in sys.modules:
            importlib.reload(sys.modules['core.mineru'])
        
        # 重新导入服务
        from service.text_extraction_service import text_extraction_service
        
        # 初始化服务
        text_extraction_service.initialize()
        
        # 创建PDF转PNG的测试图像
        png_bytes = create_pdf_like_png_image()
        print(f"创建了PDF转PNG图像，大小: {len(png_bytes)} bytes")
        
        # 验证图像是否有效
        try:
            test_img = Image.open(io.BytesIO(png_bytes))
            print(f"图像验证成功，尺寸: {test_img.size}, 模式: {test_img.mode}")
        except Exception as e:
            print(f"图像验证失败: {e}")
            return False
        
        mock_file = MockUploadFile(png_bytes, "pdf_page_1.png", "image/png")
        
        # 调用服务
        print("开始处理PDF转PNG图像...")
        response = await text_extraction_service.extract_text_boxes_from_files([mock_file])
        
        print(f"\nPDF转PNG处理结果:")
        print(f"  处理时间: {response.processing_time:.2f}秒")
        print(f"  文本框数量: {response.total_count}")
        print(f"  错误信息: {response.error_msg}")
        
        # 详细检查文本框数据
        if response.text_boxes:
            print(f"\n文本框详情:")
            for i, text_box in enumerate(response.text_boxes):
                print(f"  文本框 {i+1}:")
                print(f"    文本: '{text_box.text}'")
                print(f"    类型: {text_box.type}")
                print(f"    边界框: {text_box.bbox}")
                print(f"    页面尺寸: {text_box.page_size}")
                if text_box.page_size:
                    print(f"    页面尺寸类型: {[type(x).__name__ for x in text_box.page_size]}")
        
        return response.error_msg is None
        
    except Exception as e:
        print(f"PDF转PNG处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_corrupted_png_handling():
    """测试损坏PNG图像的处理"""
    print("\n测试损坏PNG图像的处理...")
    
    try:
        # 创建损坏的PNG数据
        corrupted_png = create_corrupted_png_bytes()
        print(f"创建了损坏的PNG数据，大小: {len(corrupted_png)} bytes")
        
        mock_file = MockUploadFile(corrupted_png, "corrupted_page.png", "image/png")
        
        # 调用服务
        response = await text_extraction_service.extract_text_boxes_from_files([mock_file])
        
        print(f"损坏PNG处理结果:")
        print(f"  处理时间: {response.processing_time:.2f}秒")
        print(f"  文本框数量: {response.total_count}")
        print(f"  错误信息: {response.error_msg}")
        
        # 对于损坏的图像，我们期望服务能够优雅处理
        return True
        
    except Exception as e:
        print(f"损坏PNG处理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始测试PDF转PNG图像处理...")
    print("=" * 60)
    
    # 正常PDF转PNG测试
    test1 = await test_pdf_to_png_processing()
    print("-" * 60)
    
    # 损坏PNG处理测试
    test2 = await test_corrupted_png_handling()
    print("-" * 60)
    
    if test1 and test2:
        print("✅ 所有PDF转PNG测试通过!")
        print("✅ 服务能够正确处理PDF转换的PNG图像")
        return True
    else:
        print("❌ 部分测试失败!")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
