FROM python:3.12-slim-bookworm
ARG VERSION=1.3.10

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG="en_US.UTF-8" \
    TZ="Asia/Shanghai"

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 升级 pip
RUN pip install --upgrade pip

# 安装 uv
RUN pip install uv

# 使用 uv 安装 mineru[core] (配置镜像源加速)
RUN uv pip install --system -U "mineru[core]==1.3.10" --index-url https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn

# 创建应用目录
WORKDIR /app

# 复制依赖文件
COPY pyproject.toml uv.lock* ./
COPY requirements.txt ./

# 安装Python依赖
RUN uv sync --frozen --no-cache --index https://pypi.tuna.tsinghua.edu.cn/simple

# 复制应用代码
COPY src/ ./src/
COPY config/ ./config/
COPY confs/ ./confs/
COPY bin/ ./bin/
COPY .env.example ./

# 设置权限
RUN chmod +x bin/start.sh && \
    ln -sf /app/.env.example /app/.env

# 创建数据目录
RUN mkdir -p /app/data

# 暴露端口
EXPOSE 8081

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/health || exit 1

# 运行应用
CMD ["/app/bin/start.sh"]
