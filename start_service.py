#!/usr/bin/env python3
"""
MinerU Service 启动脚本
支持开发模式和生产模式
"""

import argparse
import os
import sys
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(
        description="MinerU Service 启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start_service.py --dev          # 开发模式（启用重载）
  python start_service.py --prod         # 生产模式（多进程）
  python start_service.py --debug        # 调试模式（单进程 + 调试日志）
        """
    )
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--dev', action='store_true', help='开发模式（启用热重载）')
    group.add_argument('--prod', action='store_true', help='生产模式（多进程）')
    group.add_argument('--debug', action='store_true', help='调试模式（单进程 + 详细日志）')
    
    parser.add_argument('--host', default='0.0.0.0', help='服务器地址（默认: 0.0.0.0）')
    parser.add_argument('--port', type=int, default=8081, help='服务器端口（默认: 8081）')
    parser.add_argument('--workers', type=int, default=2, help='工作进程数（仅生产模式，默认: 2）')
    
    args = parser.parse_args()
    
    # 设置环境变量
    if args.dev:
        os.environ['MODE'] = 'dev'
        os.environ['DEBUG'] = 'True'
        print("🔧 启动开发模式...")
        print("  - 热重载: 启用")
        print("  - 调试日志: 启用")
        print("  - 工作进程: 1")
    elif args.debug:
        os.environ['MODE'] = 'dev'
        os.environ['DEBUG'] = 'True'
        print("🐛 启动调试模式...")
        print("  - 热重载: 禁用")
        print("  - 调试日志: 启用")
        print("  - 工作进程: 1")
    else:  # prod
        os.environ['MODE'] = 'prod'
        os.environ['DEBUG'] = 'False'
        print("🚀 启动生产模式...")
        print("  - 热重载: 禁用")
        print("  - 调试日志: 禁用")
        print(f"  - 工作进程: {args.workers}")
    
    # 设置服务器配置
    os.environ['SERVER_HOST'] = args.host
    os.environ['SERVER_PORT'] = str(args.port)
    os.environ['SERVER_WORKERS'] = str(args.workers)
    
    print(f"📡 服务器地址: http://{args.host}:{args.port}")
    print(f"📋 API 文档: http://{args.host}:{args.port}/docs")
    print("=" * 50)
    
    # 添加 src 目录到 Python 路径
    src_path = Path(__file__).parent / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))

    # 启动服务
    try:
        if args.dev:
            # 开发模式：启用热重载
            import uvicorn
            uvicorn.run(
                app="main:backend_app",
                host=args.host,
                port=args.port,
                reload=True,
                reload_dirs=["src"],
                log_level="debug",
                access_log=False,  # 禁用访问日志
            )
        elif args.debug:
            # 调试模式：单进程，详细日志
            import uvicorn
            uvicorn.run(
                app="main:backend_app",
                host=args.host,
                port=args.port,
                reload=False,
                log_level="debug",
                access_log=False,  # 禁用访问日志
            )
        else:
            # 生产模式：多进程
            import uvicorn
            uvicorn.run(
                app="main:backend_app",
                host=args.host,
                port=args.port,
                workers=args.workers,
                log_level="info",
                access_log=False,  # 禁用访问日志
            )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
