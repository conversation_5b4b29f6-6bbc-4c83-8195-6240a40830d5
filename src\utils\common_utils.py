import time
from inspect import iscoroutinefunction
from algo_base.dlog import DLog

# 全局 DLog 实例
dlog = DLog()


def singleton_func(cls):
    """ 单例函数 """
    instance = {}

    def _singleton(*args, **kwargs):
        if cls not in instance:
            instance[cls] = cls(*args, **kwargs)
        return instance[cls]

    return _singleton


def cost_time(func):
    """ 计算函数执行时间 """
    def fun(*args, **kwargs):
        t = time.perf_counter()
        result = func(*args, **kwargs)
        dlog.infof('{"name": "%s", "cost_time": %.8f}', func.__name__, time.perf_counter() - t)
        return result

    async def func_async(*args, **kwargs):
        t = time.perf_counter()
        result = await func(*args, **kwargs)
        dlog.infof('{"name": "%s", "cost_time": %.8f}', func.__name__, time.perf_counter() - t)
        return result

    if iscoroutinefunction(func):
        return func_async
    else:
        return fun
