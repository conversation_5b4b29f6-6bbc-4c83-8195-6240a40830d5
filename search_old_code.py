#!/usr/bin/env python3
"""
搜索旧代码
"""

import os
import glob

def search_in_files(pattern, directory):
    """在文件中搜索模式"""
    found_files = []
    
    # 搜索所有Python文件
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if pattern in content:
                            found_files.append(file_path)
                            print(f"发现在文件: {file_path}")
                            
                            # 显示匹配的行
                            lines = content.split('\n')
                            for i, line in enumerate(lines, 1):
                                if pattern in line:
                                    print(f"  第 {i} 行: {line.strip()}")
                except Exception as e:
                    print(f"读取文件失败 {file_path}: {e}")
    
    return found_files

def main():
    """主函数"""
    print("搜索旧的问题代码...")
    
    # 搜索 images[page_idx]
    print("搜索 'images[page_idx]':")
    files1 = search_in_files('images[page_idx]', 'src')
    
    print("\n" + "="*50)
    
    # 搜索 Image.open 相关的问题
    print("搜索 'Image.open(io_module.BytesIO(images[':")
    files2 = search_in_files('Image.open(io_module.BytesIO(images[', 'src')
    
    print("\n" + "="*50)
    
    if not files1 and not files2:
        print("✅ 没有发现旧的问题代码")
    else:
        print(f"❌ 发现问题代码在 {len(files1 + files2)} 个文件中")
    
    # 检查是否有.pyc文件
    print("\n检查是否有编译的Python文件(.pyc):")
    pyc_files = []
    for root, dirs, files in os.walk('src'):
        for file in files:
            if file.endswith('.pyc'):
                pyc_path = os.path.join(root, file)
                pyc_files.append(pyc_path)
                print(f"发现 .pyc 文件: {pyc_path}")
    
    if pyc_files:
        print(f"❌ 发现 {len(pyc_files)} 个 .pyc 文件，可能需要清理")
    else:
        print("✅ 没有发现 .pyc 文件")

if __name__ == "__main__":
    main()
