# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation
README.md
docs/
*.md

# Tests
tests/
test_*.py
*_test.py

# Development
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# Development dependencies
requirements-dev.txt

# Local data (will be mounted as volume)
data/
models/

# Environment files (except production)
.env_dev
.env_local
.env_test

# Docker files (keep deploy/ for build context)
docker-compose*.yml
.dockerignore
