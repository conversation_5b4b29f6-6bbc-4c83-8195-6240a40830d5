"""
属性工具模块
用于管理和更新系统属性字典
"""

import asyncio
import json
from pathlib import Path
from typing import Dict, Any, Optional
from algo_base.dlog import DLog
from core.settings import settings


# 全局属性字典
_property_dict: Dict[str, Any] = {}

# 全局 DLog 实例
dlog = DLog()


async def load_property_dict(file_path: Optional[str] = None) -> Dict[str, Any]:
    """
    从文件加载属性字典
    
    Args:
        file_path: 属性文件路径，如果为None则使用默认路径
        
    Returns:
        属性字典
    """
    global _property_dict
    
    try:
        if file_path is None:
            # 使用默认属性文件路径
            file_path = "config/properties.json"
        
        property_file = Path(file_path)
        
        if property_file.exists():
            dlog.infof("Loading properties from: %s", str(property_file))
            with open(property_file, 'r', encoding='utf-8') as f:
                _property_dict = json.load(f)
            dlog.infof("Loaded %d properties", len(_property_dict))
        else:
            dlog.warnf("Property file not found: %s, using default properties", str(property_file))
            _property_dict = get_default_properties()

        return _property_dict.copy()

    except Exception as e:
        dlog.errorf("Failed to load property dict: %s", str(e))
        _property_dict = get_default_properties()
        return _property_dict.copy()


async def save_property_dict(file_path: Optional[str] = None) -> bool:
    """
    保存属性字典到文件
    
    Args:
        file_path: 属性文件路径，如果为None则使用默认路径
        
    Returns:
        是否保存成功
    """
    global _property_dict
    
    try:
        if file_path is None:
            file_path = "config/properties.json"
        
        property_file = Path(file_path)
        property_file.parent.mkdir(parents=True, exist_ok=True)
        
        dlog.infof("Saving properties to: %s", str(property_file))
        with open(property_file, 'w', encoding='utf-8') as f:
            json.dump(_property_dict, f, indent=2, ensure_ascii=False)

        dlog.infof("Saved %d properties", len(_property_dict))
        return True

    except Exception as e:
        dlog.errorf("Failed to save property dict: %s", str(e))
        return False


async def update_property_dict() -> bool:
    """
    更新属性字典
    从配置文件和环境变量中更新属性
    
    Returns:
        是否更新成功
    """
    try:
        dlog.infoln("Updating property dictionary")

        # 加载基础属性
        await load_property_dict()

        # 从设置中更新属性
        update_properties_from_settings()

        # 添加运行时属性
        add_runtime_properties()

        dlog.infoln("Property dictionary updated successfully")
        return True

    except Exception as e:
        dlog.errorf("Failed to update property dict: %s", str(e))
        return False


def get_default_properties() -> Dict[str, Any]:
    """
    获取默认属性字典
    
    Returns:
        默认属性字典
    """
    return {
        "version": "1.3.10",
        "service_name": "mineru-service",
        "embedding": {
            "model_version": "large",
            "vector_dimension": 768,
            "batch_size": 32,
            "max_sequence_length": 512
        },
        "api": {
            "timeout": 30,
            "max_retries": 3,
            "rate_limit": 100
        },
        "cache": {
            "enabled": True,
            "ttl": 3600,
            "max_size": 1000
        }
    }


def update_properties_from_settings():
    """
    从设置对象更新属性
    """
    global _property_dict
    
    try:
        # 更新服务器相关属性
        _property_dict.update({
            "server": {
                "host": settings.SERVER_HOST,
                "port": settings.SERVER_PORT,
                "workers": settings.SERVER_WORKERS,
                "debug": settings.DEBUG,
                "device": settings.DEVICE
            }
        })
        
        # 更新API相关属性
        _property_dict.update({
            "api_config": {
                "prefix": settings.API_PREFIX,
                "docs_url": settings.DOCS_URL,
                "redoc_url": settings.REDOC_URL,
                "cors": {
                    "allowed_origins": settings.ALLOWED_ORIGINS,
                    "allowed_methods": settings.ALLOWED_METHODS,
                    "allowed_headers": settings.ALLOWED_HEADERS,
                    "allow_credentials": settings.IS_ALLOWED_CREDENTIALS
                }
            }
        })
        
        dlog.debugln("Properties updated from settings")

    except Exception as e:
        dlog.errorf("Failed to update properties from settings: %s", str(e))


def add_runtime_properties():
    """
    添加运行时属性
    """
    global _property_dict
    
    try:
        import time
        import platform
        import sys
        
        _property_dict.update({
            "runtime": {
                "startup_time": time.time(),
                "python_version": sys.version,
                "platform": platform.platform(),
                "architecture": platform.architecture()[0]
            }
        })
        
        dlog.debugln("Runtime properties added")

    except Exception as e:
        dlog.errorf("Failed to add runtime properties: %s", str(e))


def get_property(key: str, default: Any = None) -> Any:
    """
    获取属性值
    
    Args:
        key: 属性键，支持点分隔的嵌套键，如 'embedding.model_version'
        default: 默认值
        
    Returns:
        属性值
    """
    global _property_dict
    
    try:
        keys = key.split('.')
        value = _property_dict
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
                
        return value
        
    except Exception as e:
        dlog.errorf("Failed to get property '%s': %s", key, str(e))
        return default


def set_property(key: str, value: Any) -> bool:
    """
    设置属性值
    
    Args:
        key: 属性键，支持点分隔的嵌套键
        value: 属性值
        
    Returns:
        是否设置成功
    """
    global _property_dict
    
    try:
        keys = key.split('.')
        current = _property_dict
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 设置值
        current[keys[-1]] = value
        dlog.debugf("Property '%s' set to: %s", key, str(value))
        return True

    except Exception as e:
        dlog.errorf("Failed to set property '%s': %s", key, str(e))
        return False


def get_all_properties() -> Dict[str, Any]:
    """
    获取所有属性
    
    Returns:
        属性字典的副本
    """
    global _property_dict
    return _property_dict.copy()


async def refresh_properties() -> bool:
    """
    刷新属性字典
    重新加载配置文件和更新运行时属性
    
    Returns:
        是否刷新成功
    """
    try:
        dlog.infoln("Refreshing properties")
        return await update_property_dict()
    except Exception as e:
        dlog.errorf("Failed to refresh properties: %s", str(e))
        return False


if __name__ == "__main__":
    # 测试代码
    test_dlog = DLog()

    async def test_properties():
        # 测试更新属性字典
        success = await update_property_dict()
        test_dlog.infof("Update success: %s", str(success))

        # 测试获取属性
        version = get_property("version")
        test_dlog.infof("Version: %s", str(version))

        # 测试设置属性
        set_property("test.value", "hello")
        test_value = get_property("test.value")
        test_dlog.infof("Test value: %s", str(test_value))

        # 测试获取所有属性
        all_props = get_all_properties()
        test_dlog.infof("Total properties: %d", len(all_props))

    asyncio.run(test_properties())
