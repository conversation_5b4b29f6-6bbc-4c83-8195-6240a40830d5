from service.base_service import BaseService
from utils.common_utils import singleton_func
from core.settings import settings
from algo_base.dlog import DLog


@singleton_func
class EmbeddingService(BaseService):
    """
    嵌入式服务类
    负责处理文本和图像的嵌入向量计算
    """

    def __init__(self):
        self.dlog = DLog()
        self.name = "embedding_service"
        self.model = None
        self.device = settings.DEVICE
        super().__init__()
        
    def initialize(self):
        """
        初始化嵌入式模型
        """
        try:
            self.dlog.infof("Initializing embedding service on device: %s", self.device)
            # TODO: 在这里初始化您的嵌入式模型
            # 例如：
            # self.model = load_embedding_model(device=self.device)
            self.dlog.infoln("Embedding service initialized successfully")
        except Exception as e:
            self.dlog.errorf("Failed to initialize embedding service: %s", str(e))
            raise
    
    def get_text_embedding(self, text: str) -> list[float]:
        """
        获取文本的嵌入向量
        
        Args:
            text: 输入文本
            
        Returns:
            嵌入向量列表
        """
        try:
            # TODO: 实现文本嵌入逻辑
            # 例如：
            # embedding = self.model.encode(text)
            # return embedding.tolist()
            
            # 临时返回示例嵌入向量
            self.dlog.infof("Computing text embedding for: %s...", text[:50])
            return [0.1] * 768  # 示例768维向量
        except Exception as e:
            self.dlog.errorf("Failed to compute text embedding: %s", str(e))
            raise
    
    def get_image_embedding(self, image_url: str) -> list[float]:
        """
        获取图像的嵌入向量
        
        Args:
            image_url: 图像URL
            
        Returns:
            嵌入向量列表
        """
        try:
            # TODO: 实现图像嵌入逻辑
            # 例如：
            # image = load_image_from_url(image_url)
            # embedding = self.model.encode_image(image)
            # return embedding.tolist()
            
            # 临时返回示例嵌入向量
            self.dlog.infof("Computing image embedding for: %s", image_url)
            return [0.2] * 768  # 示例768维向量
        except Exception as e:
            self.dlog.errorf("Failed to compute image embedding: %s", str(e))
            raise
    
    def get_multimodal_embedding(self, text: str = None, image_url: str = None) -> list[float]:
        """
        获取多模态嵌入向量
        
        Args:
            text: 输入文本（可选）
            image_url: 图像URL（可选）
            
        Returns:
            嵌入向量列表
        """
        try:
            if text and image_url:
                # 多模态嵌入
                self.dlog.infoln("Computing multimodal embedding")
                # TODO: 实现多模态嵌入逻辑
                return [0.15] * 768  # 示例向量
            elif text:
                return self.get_text_embedding(text)
            elif image_url:
                return self.get_image_embedding(image_url)
            else:
                raise ValueError("Either text or image_url must be provided")
        except Exception as e:
            self.dlog.errorf("Failed to compute multimodal embedding: %s", str(e))
            raise


# 创建全局实例
embedding_server = EmbeddingService()


if __name__ == "__main__":
    # 测试代码
    dlog = DLog()
    dlog.infoln("Starting EmbeddingService test...")

    service = EmbeddingService()
    service.initialize()

    # 测试文本嵌入
    text_embedding = service.get_text_embedding("Hello, world!")
    dlog.infof("Text embedding shape: %d", len(text_embedding))

    # 测试图像嵌入
    image_embedding = service.get_image_embedding("https://example.com/image.jpg")
    dlog.infof("Image embedding shape: %d", len(image_embedding))

    # 测试多模态嵌入
    multimodal_embedding = service.get_multimodal_embedding(
        text="A beautiful sunset",
        image_url="https://example.com/sunset.jpg"
    )
    dlog.infof("Multimodal embedding shape: %d", len(multimodal_embedding))

    dlog.infoln("EmbeddingService test finished.")
