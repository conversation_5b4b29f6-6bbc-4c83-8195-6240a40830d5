#!/bin/bash

# Docker构建脚本
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

cd "$PROJECT_DIR"

# 默认参数
BUILD_TYPE="cpu"
VERSION="1.3.10"
PUSH=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        --version)
            VERSION="$2"
            shift 2
            ;;
        --push)
            PUSH=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --type TYPE      构建类型 (cpu|gpu|all) [默认: cpu]"
            echo "  --version VER    版本号 [默认: 1.3.10]"
            echo "  --push          构建后推送到镜像仓库"
            echo "  -h, --help      显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 镜像名称
IMAGE_NAME="mineru-service"
REGISTRY="your-registry.com"  # 替换为您的镜像仓库地址

echo "🚀 开始构建 MinerU Service Docker 镜像..."
echo "📋 构建类型: $BUILD_TYPE"
echo "📋 版本号: $VERSION"

build_image() {
    local type=$1
    local dockerfile="deploy/Dockerfile-$type"
    local tag="$IMAGE_NAME:$VERSION-$type"
    local latest_tag="$IMAGE_NAME:latest-$type"

    echo "🔨 构建 $type 版本镜像..."
    echo "📄 Dockerfile: $dockerfile"
    echo "🏷️  标签: $tag"
    
    if [ ! -f "$dockerfile" ]; then
        echo "❌ Dockerfile 不存在: $dockerfile"
        return 1
    fi
    
    # 构建镜像
    docker build \
        -f "$dockerfile" \
        --build-arg VERSION="$VERSION" \
        -t "$tag" \
        .
    
    if [ $? -eq 0 ]; then
        echo "✅ $type 版本构建成功!"
        
        # 如果需要推送
        if [ "$PUSH" = true ]; then
            echo "📤 推送镜像到仓库..."
            docker tag "$tag" "$REGISTRY/$tag"
            docker push "$REGISTRY/$tag"
            echo "✅ 镜像推送完成!"
        fi
    else
        echo "❌ $type 版本构建失败!"
        return 1
    fi
}

# 根据构建类型执行构建
case $BUILD_TYPE in
    cpu)
        build_image "cpu"
        ;;
    gpu)
        build_image "gpu"
        ;;
    all)
        build_image "cpu"
        build_image "gpu"
        ;;
    *)
        echo "❌ 不支持的构建类型: $BUILD_TYPE"
        echo "支持的类型: cpu, gpu, all"
        exit 1
        ;;
esac

echo "🎉 Docker 镜像构建完成!"
echo ""
echo "📋 可用镜像:"
docker images | grep "$IMAGE_NAME" | head -10

echo ""
echo "🚀 运行容器示例:"
if [ "$BUILD_TYPE" = "cpu" ] || [ "$BUILD_TYPE" = "all" ]; then
    echo "  CPU版本: docker run -d -p 8081:8081 --name mineru-cpu $IMAGE_NAME:$VERSION-cpu"
fi
if [ "$BUILD_TYPE" = "gpu" ] || [ "$BUILD_TYPE" = "all" ]; then
    echo "  GPU版本: docker run -d -p 8081:8081 --gpus all --name mineru-gpu $IMAGE_NAME:$VERSION-gpu"
fi
echo "  或使用: docker-compose up -d"
