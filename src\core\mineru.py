import numpy as np
import io
from magic_pdf.data.data_reader_writer.base import DataWriter
from magic_pdf.data.dataset import ImageDataset
from magic_pdf.config.enums import SupportedPdfParseMethod
from magic_pdf.model.doc_analyze_by_custom_model import doc_analyze
from pathlib import Path
from typing import List
from PIL import Image
from utils.file_utils import calculate_bytes_hash
from algo_base.dlog import DLog
import os
from magic_pdf.data.data_reader_writer import FileBasedDataWriter

class EmptyWriter(DataWriter):
    def __init__(self, output_dir: str):
        # DataWriter 的基类可能不接受参数，所以我们只调用 object.__init__()
        super(DataWriter, self).__init__()
        self.output_dir = output_dir

    def write(self, path: str, data: bytes) -> None:
        """Write file with data.

        Args:
            path (str): the path of file, if the path is relative path, it will be joined with parent_dir.
            data (bytes): the data want to write
        """
        fn_path = path
        if not os.path.isabs(fn_path) and len(self.output_dir) > 0:
            fn_path = os.path.join(self.output_dir, path)

        if not os.path.exists(os.path.dirname(fn_path)) and os.path.dirname(fn_path) != "":
            os.makedirs(os.path.dirname(fn_path), exist_ok=True)

        with open(fn_path, 'wb') as f:
            f.write(data)

class MineruBase(object):
    """MinerU基类"""

    def __init__(self):
        self.dlog = DLog()
        self.name = "mineru_base"

    def _get_category_name(self, category_id: int) -> str:
        """
        根据category_id获取类别名称

        Args:
            category_id: 类别ID

        Returns:
            类别名称
        """
        # MinerU的类别映射
        category_map = {
            0: 'text',
            1: 'title',
            2: 'figure',
            3: 'figure_caption',
            4: 'table',
            5: 'table_caption',
            6: 'header',
            7: 'footer',
            8: 'reference',
            9: 'equation',
            10: 'list',
            11: 'page_number',
            12: 'footnote',
            13: 'abstract',
            14: 'author',
            15: 'text',  # OCR文本
            # 可以根据需要添加更多类别
        }
        return category_map.get(category_id, f'category_{category_id}')

    def _is_page_number(self, content: str) -> bool:
        """
        检测文本内容是否为页码

        Args:
            content: 文本内容

        Returns:
            bool: 是否为页码
        """
        if not content:
            return False

        content = content.strip()

        # 1. 纯数字页码（最常见）
        if content.isdigit():
            # 页码通常在1-9999范围内
            try:
                page_num = int(content)
                if 1 <= page_num <= 9999:
                    return True
            except ValueError:
                pass

        # 2. 带连字符的页码格式：1-1, 2-3, 等
        if '-' in content:
            parts = content.split('-')
            if len(parts) == 2 and all(part.strip().isdigit() for part in parts):
                return True

        # 3. 罗马数字页码
        roman_pattern = r'^[ivxlcdmIVXLCDM]+$'
        import re
        if re.match(roman_pattern, content) and len(content) <= 10:
            return True

        # 4. 带页码标识的格式
        page_indicators = [
            r'^\d+页$',           # "1页", "23页"
            r'^第\d+页$',         # "第1页", "第23页"
            r'^Page\s*\d+$',      # "Page 1", "Page23"
            r'^P\.\s*\d+$',       # "P.1", "P. 23"
            r'^\d+/\d+$',         # "1/10", "5/20" (当前页/总页数)
            r'^\d+\s*of\s*\d+$',  # "1 of 10", "5of20"
            r'^-\d+-$',           # "-1-", "-23-"
            r'^\(\d+\)$',         # "(1)", "(23)"
            r'^\[\d+\]$',         # "[1]", "[23]"
        ]

        for pattern in page_indicators:
            if re.match(pattern, content, re.IGNORECASE):
                return True

        # 5. 简单的数字加符号组合（长度限制避免误判）
        if len(content) <= 5:
            # 去除常见的页码装饰符号后检查是否为数字
            cleaned = re.sub(r'[^\d]', '', content)
            if cleaned.isdigit() and len(cleaned) <= 4:
                # 检查原文本是否主要由数字组成
                digit_ratio = len(cleaned) / len(content)
                if digit_ratio >= 0.5:  # 至少50%是数字
                    return True

        return False

    def _is_figure_caption(self, content: str) -> bool:
        """
        检测文本内容是否为图片标题

        Args:
            content: 文本内容

        Returns:
            bool: 是否为图片标题
        """
        if not content:
            return False

        content = content.strip()

        # 图片标题通常比较短，如果太长可能是图片内的文本内容
        if len(content) > 50:
            return False

        import re

        # 首先排除明显不是图片标题的情况（优先级最高）
        exclude_patterns = [
            r'图书',     # "图书馆", "图书"
            r'图案',     # "图案设计"
            r'图形',     # "图形界面"
            r'图像',     # "图像处理"
            r'图片',     # "图片文件"
            r'制图',     # "制图软件"
            r'绘图',     # "绘图工具"
            r'地图',     # "地图导航"
            r'示意图',   # "示意图说明"
            r'流程图',   # "流程图"
            r'结构图',   # "结构图"
            r'框图',     # "框图"
            r'参考图',   # "参考图1"
            r'见图',     # "见图1"
            r'如图',     # "如图1"
            r'图.*所示', # "图1所示"
            r'图.*显示', # "图1显示"
            r'第.*图',   # "第1图"
            r'^\d+\.',   # "1. 图表" (列表项)
        ]

        for pattern in exclude_patterns:
            if re.search(pattern, content):
                return False

        # 1. 中文图片标题模式（严格匹配，避免长文本被误判）
        chinese_patterns = [
            r'^图\s*\d+$',          # "图1", "图 1", "图2"（完全匹配）
            r'^图\s*[一二三四五六七八九十]+$',  # "图一", "图二"（完全匹配）
            r'^图\s*\d+[.．：:]\s*$',    # "图1.", "图1：", "图1．"（后面没有更多内容）
            r'^图\s*\d+[.．：:]\s*[\u4e00-\u9fff]{1,10}$',  # "图1：标题"（中文标题，限制10个字符）
            r'^图\s*[一二三四五六七八九十]+[.．：:]\s*$',  # "图一.", "图二："（后面没有更多内容）
            r'^图\s*[一二三四五六七八九十]+[.．：:]\s*[\u4e00-\u9fff]{1,10}$',  # "图一：标题"
            r'^图\s*\d+[-－—]\d+$',   # "图1-1", "图2-3"（完全匹配）
            r'^图\s*\d+\.\d+$',      # "图1.1", "图2.3"（完全匹配）
        ]

        # 2. 英文图片标题模式
        english_patterns = [
            r'^Fig\s*\.?\s*\d+$',   # "Fig.1", "Fig 1"（完全匹配）
            r'^Figure\s*\d+$',      # "Figure 1", "Figure2"（完全匹配）
            r'^Fig\s*\.?\s*\d+[.:]', # "Fig.1.", "Fig.1:"
            r'^Figure\s*\d+[.:]',   # "Figure 1.", "Figure 1:"
            r'^Fig\s*\.?\s*\d+[-－—]\d+', # "Fig.1-1", "Fig 2-3"
            r'^Figure\s*\d+\.\d+',  # "Figure 1.1", "Figure 2.3"
        ]

        # 3. 其他常见模式
        other_patterns = [
            r'^\d+\.\d+\s*图',      # "1.1 图", "2.3图"
            r'^\d+[-－—]\d+\s*图',   # "1-1 图", "2-3图"
        ]

        all_patterns = chinese_patterns + english_patterns + other_patterns

        for pattern in all_patterns:
            if re.match(pattern, content, re.IGNORECASE):
                return True

        # 4. 简单的"图+数字"检测（更宽松的匹配）
        if len(content) <= 20:  # 限制长度避免误判
            # 检查是否包含"图"字和数字
            if '图' in content:
                # 提取所有数字
                numbers = re.findall(r'\d+', content)
                if numbers:
                    # 如果文本很短且包含图和数字，很可能是图片标题
                    if len(content) <= 10:
                        return True
                    # 或者如果"图"在开头附近
                    elif content.find('图') <= 2:
                        return True

        return False

    def _is_simple_figure_caption(self, content: str) -> bool:
        """
        检测简单的图片标题（如"图1"、"图2"、"Figure 1"等）
        这些通常被错误识别为figure类型

        Args:
            content: 文本内容

        Returns:
            bool: 是否为简单图片标题
        """
        if not content:
            return False

        content = content.strip()

        # 简单图片标题通常很短
        if len(content) > 15:
            return False

        import re

        # 1. 首先排除明显不是图片标题的情况
        exclude_patterns = [
            r'图形',     # "图形1", "图形界面"
            r'图像',     # "图像1", "图像处理"
            r'图案',     # "图案1", "图案设计"
            r'图片',     # "图片1", "图片文件"
            r'图书',     # "图书1", "图书馆"
            r'图标',     # "图标1", "图标设计"
            r'制图',     # "制图1", "制图软件"
            r'绘图',     # "绘图1", "绘图工具"
            r'地图',     # "地图1", "地图导航"
            r'示意图',   # "示意图1"
            r'流程图',   # "流程图1"
            r'结构图',   # "结构图1"
            r'框图',     # "框图1"
            r'参考图',   # "参考图1"
            r'见图',     # "见图1"
            r'如图',     # "如图1"
            r'图.*所示', # "图1所示"
            r'图.*显示', # "图1显示"
            r'第.*图',   # "第1图"
            r'^\d+\.',   # "1. 图表" (列表项)
        ]

        for pattern in exclude_patterns:
            if re.search(pattern, content):
                return False

        # 2. 简单图片标题的严格模式（只匹配最典型的格式）
        simple_patterns = [
            r'^图\s*\d+$',              # "图1", "图 1", "图2"
            r'^图\s*[一二三四五六七八九十]+$',    # "图一", "图二"
            r'^Fig\s*\.?\s*\d+$',       # "Fig.1", "Fig 1"
            r'^Figure\s*\d+$',          # "Figure 1", "Figure2"
            r'^图\s*\d+[.．：:]$',       # "图1.", "图1："
            r'^Fig\s*\.?\s*\d+[.:]$',   # "Fig.1.", "Fig.1:"
            r'^Figure\s*\d+[.:]$',      # "Figure 1.", "Figure 1:"
        ]

        for pattern in simple_patterns:
            if re.match(pattern, content, re.IGNORECASE):
                return True

        return False

    def _is_table_caption(self, content: str) -> bool:
        """
        检测文本内容是否为表格标题

        Args:
            content: 文本内容

        Returns:
            bool: 是否为表格标题
        """
        if not content:
            return False

        content = content.strip()

        # 表格标题通常比较短，如果太长可能是表格内的文本内容
        if len(content) > 50:
            return False

        # 表格标题的常见模式
        import re

        # 1. 中文表格标题模式
        chinese_patterns = [
            r'^表\s*\d+',           # "表1", "表 1", "表2"
            r'^表\s*[一二三四五六七八九十]+',  # "表一", "表二"
            r'^表\s*\d+[.．：:]',    # "表1.", "表1：", "表1．"
            r'^表\s*[一二三四五六七八九十]+[.．：:]',  # "表一.", "表二："
            r'^表\s*\d+[-－—]\d+',   # "表1-1", "表2-3"
            r'^表\s*\d+\.\d+',      # "表1.1", "表2.3"
        ]

        # 2. 英文表格标题模式
        english_patterns = [
            r'^Table\s*\d+',        # "Table 1", "Table2"
            r'^Table\s*\d+[.:]',    # "Table 1.", "Table 1:"
            r'^Table\s*\d+[-－—]\d+', # "Table 1-1", "Table 2-3"
            r'^Table\s*\d+\.\d+',   # "Table 1.1", "Table 2.3"
        ]

        # 3. 其他常见模式
        other_patterns = [
            r'^\d+\.\d+\s*表',      # "1.1 表", "2.3表"
            r'^\d+[-－—]\d+\s*表',   # "1-1 表", "2-3表"
        ]

        all_patterns = chinese_patterns + english_patterns + other_patterns

        for pattern in all_patterns:
            if re.match(pattern, content, re.IGNORECASE):
                return True

        # 4. 简单的"表+数字"检测（更宽松的匹配）
        if len(content) <= 20:  # 限制长度避免误判
            # 检查是否包含"表"字和数字
            if '表' in content:
                # 提取所有数字
                numbers = re.findall(r'\d+', content)
                if numbers:
                    # 如果文本很短且包含表和数字，很可能是表格标题
                    if len(content) <= 10:
                        return True
                    # 或者如果"表"在开头附近
                    elif content.find('表') <= 2:
                        return True

        # 5. 排除一些明显不是表格标题的情况
        exclude_patterns = [
            r'表示',     # "表示方法"
            r'表现',     # "表现良好"
            r'表达',     # "表达意见"
            r'表面',     # "表面现象"
            r'表格',     # "表格样式"（通常是描述性文本）
            r'代表',     # "代表人员"
            r'发表',     # "发表意见"
            r'手表',     # "手表时间"
            r'钟表',     # "钟表维修"
            r'外表',     # "外表美观"
        ]

        for pattern in exclude_patterns:
            if pattern in content:
                return False

        return False

    def _post_process_layout_results(self, layout_items: List[dict], page_size: List[int]) -> List[dict]:
        """
        对layout检测结果进行后处理，修正常见的误识别问题

        Args:
            layout_items: 原始layout检测结果
            page_size: 页面尺寸 [width, height]

        Returns:
            修正后的layout结果
        """
        if not layout_items or not page_size:
            return layout_items

        processed_items = []
        page_width, page_height = page_size[:2]

        for item in layout_items:
            category_id = item.get('category_id', 0)
            poly = item.get('poly', [])

            if len(poly) >= 8:
                # 计算bbox
                x_coords = [poly[i] for i in range(0, len(poly), 2)]
                y_coords = [poly[i] for i in range(1, len(poly), 2)]
                bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]

                x1, y1, x2, y2 = bbox
                width = x2 - x1
                height = y2 - y1
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2

                # 获取原始类型名称
                original_type = self._get_category_name(category_id)
                corrected_category_id = category_id

                # 规则1: 修正页码误识别
                # 小的、位于页面顶部或底部中央的figure很可能是页码
                if original_type == 'figure':
                    is_small = width < page_width * 0.1 and height < page_height * 0.05
                    is_center_horizontal = 0.3 <= center_x / page_width <= 0.7
                    is_top_or_bottom = center_y / page_height <= 0.1 or center_y / page_height >= 0.9

                    if is_small and is_center_horizontal and is_top_or_bottom:
                        corrected_category_id = 11  # page_number

                # 规则2: 修正图片标题误识别
                # 位于页面中央下方的小型table可能是图片标题
                elif original_type == 'table':
                    is_small = width < page_width * 0.3 and height < page_height * 0.08
                    is_center_area = 0.2 <= center_x / page_width <= 0.8
                    is_middle_to_bottom = 0.3 <= center_y / page_height <= 0.9

                    if is_small and is_center_area and is_middle_to_bottom:
                        corrected_category_id = 3  # figure_caption

                # 规则3: 修正大型图片区域
                # 大的、位于页面中央的figure_caption应该是figure
                elif original_type == 'figure_caption':
                    is_large = width > page_width * 0.3 or height > page_height * 0.2
                    is_center_area = 0.1 <= center_x / page_width <= 0.9

                    if is_large and is_center_area:
                        corrected_category_id = 2  # figure

                # 规则4: 修正表格标题误识别
                # 位于表格上方的小型figure可能是表格标题
                elif original_type == 'figure':
                    is_small = width < page_width * 0.4 and height < page_height * 0.06

                    # 检查下方是否有table
                    has_table_below = False
                    for other_item in layout_items:
                        if other_item == item:
                            continue
                        other_type = self._get_category_name(other_item.get('category_id', 0))
                        if other_type == 'table':
                            other_poly = other_item.get('poly', [])
                            if len(other_poly) >= 8:
                                other_x_coords = [other_poly[i] for i in range(0, len(other_poly), 2)]
                                other_y_coords = [other_poly[i] for i in range(1, len(other_poly), 2)]
                                other_bbox = [min(other_x_coords), min(other_y_coords), max(other_x_coords), max(other_y_coords)]

                                # 检查是否在表格上方
                                if (abs(center_x - (other_bbox[0] + other_bbox[2]) / 2) < page_width * 0.2 and
                                    y2 <= other_bbox[1] and other_bbox[1] - y2 < page_height * 0.1):
                                    has_table_below = True
                                    break

                    if is_small and has_table_below:
                        corrected_category_id = 5  # table_caption                # 更新item
                if corrected_category_id != category_id:
                    item = item.copy()
                    item['category_id'] = corrected_category_id
                    item['original_category_id'] = category_id  # 保存原始类型

                processed_items.append(item)
            else:
                processed_items.append(item)

        return processed_items

    def _filter_low_confidence_detections(self, layout_items: List[dict], confidence_threshold: float = 0.3) -> List[dict]:
        """
        过滤低置信度的layout检测结果

        Args:
            layout_items: layout检测结果
            confidence_threshold: 置信度阈值

        Returns:
            过滤后的layout结果
        """
        filtered_items = []

        for item in layout_items:
            confidence = item.get('confidence', item.get('score', 1.0))  # 默认置信度为1.0

            if confidence >= confidence_threshold:
                filtered_items.append(item)
            else:
                category_name = self._get_category_name(item.get('category_id', 0))
                self.dlog.infof("过滤低置信度检测: %s (置信度: %.3f)", category_name, confidence)

        self.dlog.infof("置信度过滤: %d -> %d 项", len(layout_items), len(filtered_items))
        return filtered_items

    def _analyze_figure_caption_relationships(self, layout_items: List[dict], ocr_items: List[dict], page_size: List[int]) -> List[dict]:
        """
        分析图片标题与图片内容的关系，修正错误的figure标记

        Args:
            layout_items: layout检测结果
            ocr_items: OCR识别结果
            page_size: 页面尺寸

        Returns:
            修正后的layout结果
        """
        if not layout_items or not page_size:
            return layout_items

        page_width, page_height = page_size[:2]
        corrected_items = []

        # 1. 找出所有可能的图片标题文本
        potential_captions = []
        simple_captions = []  # 简单标题（如"图1"、"图2"）

        for ocr_item in ocr_items:
            text_content = ocr_item.get('text', '').strip()
            bbox = self._get_bbox_from_poly(ocr_item.get('poly', []))

            if self._is_simple_figure_caption(text_content):
                simple_captions.append({
                    'ocr_item': ocr_item,
                    'text': text_content,
                    'bbox': bbox,
                    'is_simple': True
                })
            elif self._is_figure_caption(text_content):
                potential_captions.append({
                    'ocr_item': ocr_item,
                    'text': text_content,
                    'bbox': bbox,
                    'is_simple': False
                })

        # 合并所有标题
        all_captions = simple_captions + potential_captions
        self.dlog.infof("发现图片标题候选: %d个简单标题, %d个复杂标题", len(simple_captions), len(potential_captions))

        # 2. 分析layout项，特别关注被错误标记为figure的图片标题
        for item in layout_items:
            category_id = item.get('category_id', 0)
            original_type = self._get_category_name(category_id)
            poly = item.get('poly', [])

            if len(poly) >= 8:
                bbox = self._get_bbox_from_poly(poly)
                x1, y1, x2, y2 = bbox
                width = x2 - x1
                height = y2 - y1
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2

                corrected_item = item.copy()

                # 重点处理：被错误标记为figure的小型区域
                if original_type == 'figure':
                    # 检查尺寸：图片标题通常比较小
                    is_small = (width < page_width * 0.4 and height < page_height * 0.1)

                    if is_small:
                        # 检查是否有对应的OCR文本是图片标题
                        has_caption_text = False
                        for caption_info in all_captions:
                            caption_bbox = caption_info['bbox']
                            if caption_bbox and self._bbox_overlap(bbox, caption_bbox, threshold=0.3):
                                has_caption_text = True
                                break

                        # 检查下方是否有更大的图片区域
                        has_figure_below = False
                        for other_item in layout_items:
                            if other_item == item:
                                continue
                            other_type = self._get_category_name(other_item.get('category_id', 0))
                            other_poly = other_item.get('poly', [])

                            if len(other_poly) >= 8:
                                other_bbox = self._get_bbox_from_poly(other_poly)
                                other_width = other_bbox[2] - other_bbox[0]
                                other_height = other_bbox[3] - other_bbox[1]

                                # 检查是否是更大的图片区域，且在当前区域下方
                                is_larger = (other_width > width * 1.5 or other_height > height * 2)
                                is_below = (other_bbox[1] > y1 and other_bbox[1] - y2 < page_height * 0.2)
                                is_horizontally_aligned = abs((other_bbox[0] + other_bbox[2]) / 2 - center_x) < page_width * 0.3

                                if (other_type in ['figure', 'table'] and is_larger and is_below and is_horizontally_aligned):
                                    has_figure_below = True
                                    break

                        # 如果满足图片标题的条件，修正类型
                        # 但是要求至少满足两个条件中的一个，并且要有足够的置信度
                        confidence_score = 0
                        reasons = []

                        if has_caption_text:
                            confidence_score += 0.7
                            reasons.append('有标题文本')

                        if has_figure_below:
                            confidence_score += 0.5
                            reasons.append('下方有图片')

                        # 额外的置信度检查
                        if is_small and width < page_width * 0.2 and height < page_height * 0.05:
                            confidence_score += 0.3
                            reasons.append('尺寸很小')

                        # 只有置信度足够高才进行修正
                        if confidence_score >= 0.7:
                            corrected_item['category_id'] = 3  # figure_caption
                            corrected_item['original_category_id'] = category_id
                            corrected_item['correction_reason'] = 'small_figure_with_high_confidence'
                            corrected_item['confidence_score'] = confidence_score
                            self.dlog.infof("修正小型figure为figure_caption: %s (置信度: %.2f, 原因: %s)", str(bbox), confidence_score, ', '.join(reasons))
                        else:
                            pass

                # 处理：被错误标记为其他类型的图片标题
                elif original_type in ['table', 'text', 'title']:
                    # 检查是否有图片标题文本
                    for caption_info in all_captions:
                        caption_bbox = caption_info['bbox']
                        if caption_bbox and self._bbox_overlap(bbox, caption_bbox, threshold=0.5):
                            # 检查尺寸是否合理
                            is_reasonable_size = (width < page_width * 0.5 and height < page_height * 0.12)
                            if is_reasonable_size:
                                corrected_item['category_id'] = 3  # figure_caption
                                corrected_item['original_category_id'] = category_id
                                corrected_item['correction_reason'] = 'has_caption_text'
                                caption_type = '简单' if caption_info.get('is_simple', False) else '复杂'
                                self.dlog.infof("修正%s为figure_caption: '%s' (%s标题) %s", original_type, caption_info['text'], caption_type, str(bbox))
                                break

                corrected_items.append(corrected_item)
            else:
                corrected_items.append(item)

        return corrected_items

    def _get_bbox_from_poly(self, poly: List[float]) -> List[float]:
        """从多边形坐标获取边界框"""
        if len(poly) < 8:
            return [0, 0, 0, 0]

        x_coords = [poly[i] for i in range(0, len(poly), 2)]
        y_coords = [poly[i] for i in range(1, len(poly), 2)]
        return [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]

    def _is_page_number_by_position(self, bbox, page_size) -> bool:
        """
        基于位置判断是否为页码
        页码通常出现在页面的顶部或底部中央位置

        Args:
            bbox: [x1, y1, x2, y2] 边界框坐标
            page_size: [width, height] 页面尺寸

        Returns:
            bool: 是否可能是页码位置
        """
        if not bbox or len(bbox) < 4 or not page_size or len(page_size) < 2:
            return False

        x1, y1, x2, y2 = bbox[:4]
        page_width, page_height = page_size[:2]

        # 计算文本框的中心点和尺寸
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
        box_width = x2 - x1
        box_height = y2 - y1

        # 页码通常是小的文本框
        if box_width > page_width * 0.2 or box_height > page_height * 0.1:
            return False

        # 检查是否在页面的水平中央区域（中间50%区域）
        horizontal_center_zone = 0.25 <= center_x / page_width <= 0.75

        # 检查是否在页面的顶部或底部区域
        top_zone = center_y / page_height <= 0.15  # 顶部15%
        bottom_zone = center_y / page_height >= 0.85  # 底部15%

        # 页码通常在水平中央且在顶部或底部
        return horizontal_center_zone and (top_zone or bottom_zone)

    def _bbox_overlap(self, bbox1, bbox2, threshold=0.5):
        """
        检查两个bbox是否重叠

        Args:
            bbox1: [x1, y1, x2, y2]
            bbox2: [x1, y1, x2, y2]
            threshold: 重叠阈值

        Returns:
            bool: 是否重叠
        """
        if len(bbox1) < 4 or len(bbox2) < 4:
            return False

        x1_1, y1_1, x2_1, y2_1 = bbox1[:4]
        x1_2, y1_2, x2_2, y2_2 = bbox2[:4]

        # 计算重叠区域
        overlap_x1 = max(x1_1, x1_2)
        overlap_y1 = max(y1_1, y1_2)
        overlap_x2 = min(x2_1, x2_2)
        overlap_y2 = min(y2_1, y2_2)

        # 检查是否有重叠
        if overlap_x1 >= overlap_x2 or overlap_y1 >= overlap_y2:
            return False

        # 计算重叠面积
        overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)

        # 计算较小bbox的面积
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        smaller_area = min(area1, area2)

        # 检查重叠比例
        if smaller_area > 0:
            overlap_ratio = overlap_area / smaller_area
            return overlap_ratio >= threshold

        return False

    def _smart_type_detection(self, text_content: str, detected_type: str, force_title_keywords: list = None) -> str:
        """
        基于文本内容的智能类型检测

        Args:
            text_content: 文本内容
            detected_type: MinerU检测的类型

        Returns:
            修正后的类型
        """
        if not text_content or not text_content.strip():
            return detected_type

        content = text_content.strip()

        # 页码检测（优先级最高）
        if self._is_page_number(content):            return 'page_number'

        # 图片标题检测（优先级第二）
        # 但是要排除原本就是figure类型的情况（那些可能是图片内容，不是标题）
        if self._is_figure_caption(content) and detected_type != 'figure':            return 'figure_caption'

        # 表格标题检测（优先级第三）
        # 但是要排除原本就是table类型的情况（那些可能是表格内容，不是标题）
        if self._is_table_caption(content) and detected_type != 'table':            return 'table_caption'

        # 强制标题关键词检查（优先级第四）
        if force_title_keywords:
            for keyword in force_title_keywords:
                if keyword in content:                    return 'title'

        # 标题特征检测
        title_indicators = [
            # 中文标题关键词
            '报告', '摘要', '总结', '概述', '亮点', '要点', '简介', '前言',
            '背景', '目标', '方案', '计划', '策略', '分析', '结论', '建议',
            '第一章', '第二章', '第三章', '第四章', '第五章',
            '一、', '二、', '三、', '四、', '五、', '六、', '七、', '八、', '九、', '十、',
            '十一、', '十二、', '十三、', '十四、', '十五、', '十六、', '十七、', '十八、', '十九、', '二十、',
            '1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.',
            # 英文标题关键词
            'Summary', 'Overview', 'Introduction', 'Background', 'Conclusion',
            'Analysis', 'Report', 'Chapter', 'Section', 'Part',
        ]

        # 正文特征检测
        text_indicators = [
            # 长句子特征
            '。', '，', '；', '：', '！', '？',  # 中文标点
            '.', ',', ';', ':', '!', '?',     # 英文标点
            '的', '了', '在', '是', '有', '和', '与', '或', '但', '而且', '因此', '所以',
            'the', 'and', 'or', 'but', 'however', 'therefore', 'because',
        ]

        # 计算特征分数
        title_score = 0
        text_score = 0

        # 1. 长度特征
        if len(content) <= 15:
            title_score += 2
        elif len(content) <= 30:
            title_score += 1
        else:
            text_score += 2

        # 2. 关键词特征
        for indicator in title_indicators:
            if indicator in content:
                title_score += 3
                break

        for indicator in text_indicators:
            if indicator in content:
                text_score += 2  # 增加权重

        # 3. 标点符号特征（修正过度敏感问题）
        punctuation_count = sum(1 for char in content if char in '。，；：！？.,;:!?')
        if punctuation_count == 0:
            title_score += 2
        elif punctuation_count == 1:
            # 只有1个标点符号，可能是标题（如"概述：基本情况"）
            if content.count('：') == 1 or content.count(':') == 1:
                title_score += 1  # 冒号常用于标题
            else:
                text_score += 1   # 其他单个标点轻微倾向于正文
        elif punctuation_count >= 2:
            text_score += 2  # 多个标点符号更可能是正文

        # 4. 数字和年份特征
        if any(year in content for year in ['2024', '2023', '2022', '2021', '2020']):
            if len(content) <= 20:
                title_score += 1

        # 5. 特殊格式特征
        if content.startswith(('第', '一、', '二、', '三、', '四、', '五、')) or \
           content.endswith(('章', '节', '部分', '篇')):
            title_score += 3

        # 6. 句子完整性特征
        if content.endswith(('。', '.', '！', '!', '？', '?')):
            text_score += 2  # 完整句子更可能是正文

        # 7. 数字和百分比特征
        if any(char in content for char in ['%', '元', '亿', '万', '千']):
            text_score += 1  # 包含数字金额的更可能是正文

        # 8. 选择题特征（新增）
        if content.startswith(('A.', 'B.', 'C.', 'D.', 'a.', 'b.', 'c.', 'd.')):
            text_score += 3  # 选择题选项更可能是正文

        # 9. 题目编号特征（新增）
        if content.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.')):
            text_score += 3  # 题目编号更可能是正文

        # 10. 括号特征（新增）
        if '（' in content or '）' in content or '(' in content or ')' in content:
            text_score += 2  # 包含括号的更可能是正文

        # 11. 位置特征（新增）- 基于MinerU原始类型的权重调整
        if detected_type == 'title':
            title_score += 1  # 给MinerU原始判断一定权重
        elif detected_type == 'text':
            text_score += 1

        # 12. 商业文档特征（新增）
        business_keywords = ['公司', '业务', '收入', '利润', '市场', '客户', '产品', '服务', '管理', '发展', '增长', '分析', '报告']
        if any(keyword in content for keyword in business_keywords):
            # 商业关键词的文本，如果较短可能是标题
            if len(content) <= 25:
                title_score += 1

        # 15. 附录和章节特征（新增）
        if content.startswith(('附录', '附件', 'Appendix', 'Annex')):
            title_score += 2  # 附录通常是标题

        # 13. 完整性判断优化
        # 如果文本很长但是MinerU原始识别为title，可能是标题性的描述
        if len(content) > 20 and detected_type == 'title':
            title_score += 2  # 增加对MinerU原始判断的信任

        # 14. 多页一致性检查（新增）
        # 对于多页文档，增加对MinerU原始判断的信任度
        # 这有助于保持页面间的一致性
        if detected_type == 'title':
            title_score += 0.5  # 轻微增加对原始title判断的信任
        elif detected_type == 'text':
            text_score += 0.5   # 轻微增加对原始text判断的信任

        # 决策逻辑
        if title_score > text_score + 1:
            corrected_type = 'title'
        elif text_score > title_score + 1:
            corrected_type = 'text'
        else:
            # 分数接近时，保持原检测结果
            corrected_type = detected_type

        # 记录修正信息
        if corrected_type != detected_type:
            self.dlog.infof("智能类型修正: '%s...' %s -> %s (title_score=%.3f, text_score=%.3f)", content[:20], detected_type, corrected_type, title_score, text_score)

        return corrected_type

    def gen_text_boxes(self, images: List[bytes | Image.Image | np.ndarray | str | Path], img_folder: Path) -> List[dict]:
        """
        将图片基于视觉分割，生成若干的文本框，并返回文本框的列表

        Args:
            images (List[bytes | Image.Image | np.ndarray | str | Path]): 图片列表

        Returns:
            List[dict]: 文本框列表
        """
        self.dlog.infof("开始处理 %d 张图片", len(images))
        text_boxes = []
        for page_idx, image in enumerate(images):            # 将图片转换为字节数据来计算哈希
            if isinstance(image, bytes):
                image_bytes = image
            elif isinstance(image, (str, Path)):
                # 如果是文件路径，读取文件内容
                with open(image, 'rb') as f:
                    image_bytes = f.read()
            elif isinstance(image, Image.Image):
                # 如果是PIL图片，转换为字节
                import io
                buffer = io.BytesIO()
                image.save(buffer, format='PNG')
                image_bytes = buffer.getvalue()
            elif isinstance(image, np.ndarray):
                # 如果是numpy数组，转换为PIL再转字节
                pil_image = Image.fromarray(image)
                import io
                buffer = io.BytesIO()
                pil_image.save(buffer, format='PNG')
                image_bytes = buffer.getvalue()
            else:
                # 不支持的图像类型，记录错误并跳过
                self.dlog.errorf("不支持的图像类型: %s, 跳过处理", type(image))
                continue

            # 验证图像字节数据是否有效
            try:
                import io as io_module
                test_image = Image.open(io_module.BytesIO(image_bytes))
                test_image.verify()  # 验证图像完整性
                self.dlog.infof("图像验证成功，尺寸: %dx%d", test_image.width, test_image.height)
            except Exception as validation_error:
                self.dlog.errorf("图像数据验证失败: %s, 跳过处理", str(validation_error))
                continue

            hash_value = calculate_bytes_hash(image_bytes)
            image_dir = os.path.join(img_folder, hash_value, "images")
            os.makedirs(image_dir, exist_ok=True)
            image_writer = EmptyWriter(image_dir)

            # 保存原始图片到image_dir
            if isinstance(image, (str, Path)):
                # 如果是文件路径，复制文件到目标目录
                import shutil
                filename = os.path.basename(str(image))
                target_path = os.path.join(image_dir, filename)
                shutil.copy2(str(image), target_path)
            else:
                # 保存图片字节数据
                filename = f"page_{page_idx}.png"
                image_writer.write(filename, image_bytes)

            # proc
            ## Create Dataset Instance
            ds = ImageDataset(image)

            ## inference
            if ds.classify() == SupportedPdfParseMethod.OCR:
                infer_result = ds.apply(doc_analyze, ocr=True)
                ## pipeline
                pipe_result = infer_result.pipe_ocr_mode(image_writer)

            else:
                infer_result = ds.apply(doc_analyze, ocr=False)
                ## pipeline
                pipe_result = infer_result.pipe_txt_mode(image_writer)



            ### 从 infer_result 中获取准确的bbox信息
            try:
                model_result = infer_result.get_infer_res()
                self.dlog.infof("model_result 类型: %s", str(type(model_result)))

                # 处理推理结果
                text_boxes_with_bbox = []

                if isinstance(model_result, list):
                    self.dlog.infof("model_result包含 %d 个页面数据，当前处理图片 %d", len(model_result), page_idx)
                    # MinerU每次只处理一张图片，所以model_result[0]就是当前图片的数据
                    # 不管当前是第几张图片，都使用索引0来获取数据
                    if len(model_result) > 0:
                        page_data = model_result[0]  # 总是使用索引0                    else:                        raise IndexError(f"model_result为空")

                    if isinstance(page_data, dict):
                            self.dlog.infof("页面数据键: %s", str(list(page_data.keys())))

                            # 获取页面信息（包含原始图片尺寸）
                            page_info = page_data.get('page_info', {})
                            original_width = page_info.get('width')
                            original_height = page_info.get('height')                            # 检查layout检测结果
                            if 'layout_dets' in page_data:
                                layout_data = page_data['layout_dets']
                                self.dlog.infof("发现Layout结果，数量: %d", len(layout_data))

                                if isinstance(layout_data, list):
                                    # 分离layout检测和OCR结果
                                    layout_items = []  # 只有layout信息，没有文本
                                    ocr_items = []     # 有文本内容的OCR结果

                                    for item_idx, item in enumerate(layout_data):
                                        if isinstance(item, dict):
                                            text_content = item.get('text', '')
                                            category_id = item.get('category_id', 0)

                                            if text_content:  # 有文本内容的是OCR结果
                                                ocr_items.append(item)
                                            else:  # 没有文本内容的是layout检测结果
                                                layout_items.append(item)

                                    self.dlog.infof("Layout检测项: %d, OCR识别项: %d", len(layout_items), len(ocr_items))

                                    # 1. 过滤低置信度的检测结果
                                    if layout_items:
                                        layout_items = self._filter_low_confidence_detections(layout_items, confidence_threshold=0.25)

                                    # 2. 分析图片标题与图片内容的关系，修正错误的figure标记
                                    if layout_items and ocr_items and original_width and original_height:
                                        layout_items = self._analyze_figure_caption_relationships(
                                            layout_items,
                                            ocr_items,
                                            [original_width, original_height]
                                        )                                    # 3. 对layout结果进行后处理，修正常见误识别
                                    if layout_items and original_width and original_height:
                                        layout_items = self._post_process_layout_results(
                                            layout_items,
                                            [original_width, original_height]
                                        )
                                        self.dlog.infof("Layout后处理完成，修正后的layout项: %d", len(layout_items))

                                    # 处理有文本内容的OCR结果，但尝试匹配layout类型
                                    for item_idx, ocr_item in enumerate(ocr_items):
                                        text_content = ocr_item.get('text', '')
                                        ocr_category_id = ocr_item.get('category_id', 15)  # OCR通常是15
                                        ocr_poly = ocr_item.get('poly', [])

                                        if len(ocr_poly) >= 8:
                                            # 转换OCR的poly为bbox
                                            x_coords = [ocr_poly[i] for i in range(0, len(ocr_poly), 2)]
                                            y_coords = [ocr_poly[i] for i in range(1, len(ocr_poly), 2)]
                                            ocr_bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]

                                            # 尝试找到匹配的layout项来获取正确的类型
                                            matched_type = None
                                            for layout_item in layout_items:
                                                layout_poly = layout_item.get('poly', [])
                                                if len(layout_poly) >= 8:
                                                    layout_x_coords = [layout_poly[i] for i in range(0, len(layout_poly), 2)]
                                                    layout_y_coords = [layout_poly[i] for i in range(1, len(layout_poly), 2)]
                                                    layout_bbox = [min(layout_x_coords), min(layout_y_coords), max(layout_x_coords), max(layout_y_coords)]

                                                    # 检查OCR bbox是否在layout bbox内或重叠（使用更宽松的阈值）
                                                    if self._bbox_overlap(ocr_bbox, layout_bbox, threshold=0.3):
                                                        matched_type = self._get_category_name(layout_item.get('category_id', 0))
                                                        break

                                            # 如果没有匹配到layout类型，使用OCR的类型
                                            if not matched_type:
                                                matched_type = self._get_category_name(ocr_category_id)

                                            # 应用智能类型检测
                                            smart_type = self._smart_type_detection(text_content, matched_type)

                                            # 将bbox坐标转换回原始图片尺寸
                                            if original_width and original_height:
                                                # 计算实际的原始图片尺寸（从image_bytes获取）
                                                import io as io_module
                                                try:
                                                    actual_image = Image.open(io_module.BytesIO(image_bytes))
                                                    actual_width, actual_height = actual_image.size
                                                except Exception as img_error:
                                                    self.dlog.errorf("无法打开图像字节数据: %s", str(img_error))
                                                    # 如果无法打开图像，使用原始尺寸
                                                    actual_width, actual_height = original_width, original_height

                                                # 计算缩放比例
                                                scale_x = actual_width / original_width
                                                scale_y = actual_height / original_height

                                                # 转换bbox坐标并转为整数
                                                corrected_bbox = [
                                                    int(round(ocr_bbox[0] * scale_x)),
                                                    int(round(ocr_bbox[1] * scale_y)),
                                                    int(round(ocr_bbox[2] * scale_x)),
                                                    int(round(ocr_bbox[3] * scale_y))
                                                ]
                                                self.dlog.infof("坐标转换: 原始=%s -> 修正=%s, 缩放比例=(%.3f, %.3f)", str(ocr_bbox), str(corrected_bbox), scale_x, scale_y)
                                            else:
                                                # 即使没有缩放，也转换为整数
                                                corrected_bbox = [int(round(coord)) for coord in ocr_bbox] if ocr_bbox else ocr_bbox

                                            text_box_data = {
                                                'text': text_content,
                                                'bbox': corrected_bbox,
                                                'type': smart_type,  # 使用智能检测的类型
                                                'page_idx': page_idx,
                                                'page_size': [int(actual_width), int(actual_height)] if original_width and original_height else None
                                            }
                                            text_boxes_with_bbox.append(text_box_data)

                                    # 处理没有文本内容的layout项（如图片、表格等）
                                    for item_idx, layout_item in enumerate(layout_items):
                                        category_id = layout_item.get('category_id', 0)
                                        poly = layout_item.get('poly', [])

                                        if len(poly) >= 8:
                                            x_coords = [poly[i] for i in range(0, len(poly), 2)]
                                            y_coords = [poly[i] for i in range(1, len(poly), 2)]
                                            bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]

                                            type_name = self._get_category_name(category_id)

                                            # 只添加非文本类型的layout项（如图片、表格等）
                                            if type_name not in ['text']:
                                                # 将bbox坐标转换回原始图片尺寸
                                                if original_width and original_height:
                                                    # 计算实际的原始图片尺寸（从image_bytes获取）
                                                    import io as io_module
                                                    try:
                                                        actual_image = Image.open(io_module.BytesIO(image_bytes))
                                                        actual_width, actual_height = actual_image.size
                                                    except Exception as img_error:
                                                        self.dlog.errorf("无法打开图像字节数据: %s", str(img_error))
                                                        # 如果无法打开图像，使用原始尺寸
                                                        actual_width, actual_height = original_width, original_height

                                                    # 计算缩放比例
                                                    scale_x = actual_width / original_width
                                                    scale_y = actual_height / original_height

                                                    # 转换bbox坐标并转为整数
                                                    corrected_bbox = [
                                                        int(round(bbox[0] * scale_x)),
                                                        int(round(bbox[1] * scale_y)),
                                                        int(round(bbox[2] * scale_x)),
                                                        int(round(bbox[3] * scale_y))
                                                    ]
                                                    self.dlog.infof("Layout坐标转换: 原始=%s -> 修正=%s, 缩放比例=(%.3f, %.3f)", str(bbox), str(corrected_bbox), scale_x, scale_y)
                                                else:
                                                    # 即使没有缩放，也转换为整数
                                                    corrected_bbox = [int(round(coord)) for coord in bbox] if bbox else bbox

                                                # 对layout项进行智能类型修正
                                                final_type = type_name
                                                page_size = [int(actual_width), int(actual_height)] if original_width and original_height else None

                                                # 1. 对于figure类型，检查是否可能是页码（基于位置）
                                                if type_name == 'figure' and corrected_bbox and page_size:
                                                    if self._is_page_number_by_position(corrected_bbox, page_size):
                                                        final_type = 'page_number'                                                # 2. 对于被误识别为figure_caption的layout项，修正为figure
                                                # 因为没有文本内容的layout项不应该是标题
                                                elif type_name == 'figure_caption':
                                                    final_type = 'figure'

                                                # 3. 对于被误识别为table_caption的layout项，修正为table
                                                elif type_name == 'table_caption':
                                                    final_type = 'table'

                                                text_box_data = {
                                                    'text': '',
                                                    'bbox': corrected_bbox,
                                                    'type': final_type,
                                                    'page_idx': page_idx,
                                                    'page_size': [int(actual_width), int(actual_height)] if original_width and original_height else None
                                                }
                                                text_boxes_with_bbox.append(text_box_data)

                # 去重处理：移除重复的文本框
                if text_boxes_with_bbox:
                    self.dlog.infof("从infer_result中找到 %d 个带bbox的文本框", len(text_boxes_with_bbox))

                    unique_text_boxes = []
                    seen_boxes = set()

                    for text_box in text_boxes_with_bbox:
                        # 创建唯一标识：基于文本内容和bbox坐标
                        box_key = (
                            text_box.get('text', ''),
                            tuple(text_box.get('bbox', [])),
                            text_box.get('page_idx', 0)
                        )

                        if box_key not in seen_boxes:
                            seen_boxes.add(box_key)
                            unique_text_boxes.append(text_box)
                    text_boxes.extend(unique_text_boxes)
                    continue  # 跳过get_content_list的处理

            except Exception as e:
                self.dlog.errorf("从infer_result获取bbox失败: %s", str(e))
                import traceback
                self.dlog.errorf("详细错误: %s", traceback.format_exc())

            ### 如果上面没有找到bbox，回退到原来的方法
            text_boxes_page = pipe_result.get_content_list(image_dir)

            for i, text_box in enumerate(text_boxes_page):
                text_box['page_idx'] = page_idx

            text_boxes.extend(text_boxes_page)

        return text_boxes
    