# MinerU Service 1.3.10

基于 FastAPI 的智能文档解析服务，专为 MinerU 1.3.10 版本设计，提供高性能的 PDF 文档解析和文本提取功能。

## ✨ 功能特性

### 🔧 核心功能
- 📄 **智能PDF解析**: 支持复杂PDF文档的智能解析和文本提取
- 🎯 **精准文本识别**: 智能识别标题、正文、表格、图片标题等不同类型内容
- 🔍 **Layout分析**: 先进的版面分析，准确识别文档结构
- 📊 **表格提取**: 精确提取表格内容和结构
- 🖼️ **图片处理**: 智能识别和处理文档中的图片和图表

### 🚀 技术特性
- ⚡ **高性能**: 基于 FastAPI 的异步处理架构
- 🔧 **现代化**: 使用 UV 进行 Python 包管理
- 📝 **类型安全**: 完整的类型注解支持
- 🐳 **容器化**: Docker 支持（GPU版本）
- 📊 **监控**: 健康检查和性能监控
- 🔍 **文档**: 自动生成的 API 文档
- 🎛️ **配置**: 灵活的配置管理系统
- 📋 **日志系统**: 统一的 DLog 结构化日志

### 🎨 优化特性
- 🧹 **日志优化**: 智能过滤无用日志，保持日志清洁
- 🔄 **智能重试**: 自动重试机制提高稳定性
- 📈 **性能优化**: 针对不同硬件环境的性能优化
- 🛡️ **错误处理**: 完善的错误处理和恢复机制

## 🚀 快速开始

### 方法一：使用 Docker Compose（推荐）

```bash
# GPU版本（需要NVIDIA Docker支持）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 方法二：使用自动安装脚本

#### Linux/macOS
```bash
# 安装生产依赖
./install_dependencies.sh --uv

# 安装开发依赖
./install_dependencies.sh --uv --dev

# 安装 GPU 依赖
./install_dependencies.sh --uv --gpu
```

#### Windows
```cmd
# 安装生产依赖
install_dependencies.bat --uv

# 安装开发依赖
install_dependencies.bat --uv --dev

# 安装 GPU 依赖
install_dependencies.bat --uv --gpu
```

### 方法三：使用 UV（推荐）

```bash
# 1. 安装 UV（如果尚未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 安装依赖
uv sync

# 3. 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows
```

### 方法四：使用传统 pip

```bash
# 1. 创建虚拟环境
python -m venv .venv

# 2. 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 开发环境
pip install -r requirements-dev.txt

# GPU 环境
pip install -r requirements-gpu.txt
```

### 方法五：使用 Makefile

```bash
# 安装生产依赖
make install

# 安装开发依赖
make install-dev

# 安装 GPU 依赖
make install-gpu

# 设置开发环境
make dev-setup

# 构建和运行 Docker
make docker-build
make docker-run
```

## ⚙️ 配置

### 🔧 统一配置文件

项目现在使用统一的 `.env` 配置文件，支持 CPU 和 GPU 模式快速切换：

#### 方法一：使用切换脚本（推荐）
```bash
# 切换到 GPU 模式
python switch_device.py --gpu

# 切换到 CPU 模式
python switch_device.py --cpu

# 切换到 GPU 模式并启用调试
python switch_device.py --gpu --debug

# 查看当前配置
python switch_device.py --show
```

#### 方法二：手动编辑 .env 文件
```bash
# 编辑 .env 文件
nano .env

# 修改关键配置项：
# DEVICE=cuda    # GPU模式
# DEVICE=cpu     # CPU模式
```

### 📋 主要配置项

| 配置项 | GPU模式 | CPU模式 | 说明 |
|--------|---------|---------|------|
| `DEVICE` | `cuda` | `cpu` | 设备类型 |
| `SERVER_WORKERS` | `2` | `4` | 工作进程数 |
| `DEBUG` | `False` | `True` | 调试模式 |
| `CONSUL_ENABLED` | `false` | `false` | 是否启用 Consul |

### 🔍 Consul 服务发现

项目支持 **Consul** 服务发现和注册功能：

#### 配置参数
| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `CONSUL_ENABLED` | `false` | 是否启用 Consul 服务发现 |
| `CONSUL_HOST` | `localhost` | Consul 服务器地址 |
| `CONSUL_PORT` | `8500` | Consul 服务器端口 |
| `SERVICE_NAME` | `mineru-service` | 当前服务名称 |
| `SERVICE_HOST` | `localhost` | 当前服务地址 |
| `MINERU_SERVICE_NAME` | `mineru-service` | MinerU 服务名称 |

#### 启用 Consul
```bash
# 在 .env 文件中设置
CONSUL_ENABLED=true
CONSUL_HOST=your-consul-host
CONSUL_PORT=8500
SERVICE_NAME=mineru-service-prod
SERVICE_HOST=your-service-host
```

### 📋 日志系统

项目使用统一的 **DLog** 日志系统，提供结构化的日志输出：

#### 基本使用
```python
from algo_base.dlog import DLog

# 创建日志实例
dlog = DLog()

# 基本日志
dlog.infoln("这是一条信息日志")
dlog.errorln("这是一条错误日志")

# 格式化日志
dlog.infof("用户 %s 登录成功", username)
dlog.errorf("处理失败，错误代码: %d", error_code)

# 带 trace_id 的日志
dlog_with_trace = DLog("request-123")
dlog_with_trace.infof("处理请求: %s", request_data)
```

#### 日志级别
- `debugln()` / `debugf()` - 调试信息
- `infoln()` / `infof()` - 一般信息
- `warnln()` / `warnf()` - 警告信息
- `errorln()` / `errorf()` - 错误信息

## 🏃 运行服务

### 🚀 推荐方式：使用启动脚本
```bash
# 开发模式（热重载 + 调试日志）
python start_service.py --dev

# 生产模式（多进程）
python start_service.py --prod

# 调试模式（单进程 + 详细日志）
python start_service.py --debug

# 自定义配置
python start_service.py --prod --host 127.0.0.1 --port 8080 --workers 4
```

### 传统方式
```bash
# 使用 UV
uv run python src/main.py

# 使用 Python
python src/main.py

# 使用 Makefile
make run
```

### 生产模式
```bash
# 使用 UV
uv run uvicorn src.main:backend_app --host 0.0.0.0 --port 8081

# 使用 uvicorn
uvicorn src.main:backend_app --host 0.0.0.0 --port 8081 --workers 4
```

## 🐳 Docker 部署

### 快速开始（推荐）

#### 使用 Docker Compose
```bash
# GPU版本（需要NVIDIA Docker支持）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 使用构建脚本
```bash
# 构建GPU版本
./bin/docker-build.sh --type gpu

# 运行GPU版本
./bin/docker-run.sh --type gpu
```

#### 使用 Makefile
```bash
# GPU版本
make docker-build      # 构建GPU镜像
make docker-run        # 运行GPU容器

# Docker Compose
make docker-compose-up   # 启动GPU版本
make docker-compose-down # 停止服务

# 管理
make docker-logs       # 查看日志
make docker-stop       # 停止容器
make docker-clean      # 清理镜像
```

### 手动部署

#### GPU 版本
```bash
# 构建镜像
docker build -f deploy/Dockerfile-gpu -t mineru-service:1.3.10-gpu .

# 运行容器（需要NVIDIA Docker支持）
docker run -d \
  --name mineru-service \
  --gpus all \
  -p 8081:8081 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/config:/app/config \
  -e DEVICE=cuda \
  mineru-service:1.3.10-gpu
```

### Docker 特性

- ✅ **GPU 加速**: 专为 GPU 环境优化
- ✅ **健康检查**: 自动监控服务状态
- ✅ **数据持久化**: 卷挂载支持
- ✅ **环境配置**: 灵活的环境变量配置
- ✅ **日志管理**: 完善的日志输出
- ✅ **一键部署**: 简化的部署流程

详细的Docker部署指南请参考 [DOCKER.md](DOCKER.md)

## 📖 API 文档

服务启动后，可以通过以下地址访问：

### 🌐 服务地址
- **主服务**: http://localhost:8081

### 📚 API 文档
- **Swagger UI**: http://localhost:8081/docs
- **ReDoc**: http://localhost:8081/redoc  
- **OpenAPI JSON**: http://localhost:8081/openapi.json

### 🔍 监控端点
- **健康检查**: `GET /health`
- **服务状态**: `GET /health-status/`
- **获取状态**: `GET /health-status/get`
- **设置状态**: `GET /health-status/set?code=200`

### 🔧 主要API端点
- **文本提取**: `POST /api/v1/text-extraction/extract`
  - 上传PDF文件进行智能解析和文本提取
  - 支持多文件批量处理
  - 返回结构化的文本内容和元数据

### 📝 API 使用示例

```bash
# 健康检查
curl http://localhost:8081/health

# 文本提取
curl -X POST "http://localhost:8081/api/v1/text-extraction/extract" \
  -H "Content-Type: multipart/form-data" \
  -F "files=@document.pdf"
```

## 🛠️ 开发工具

### 代码格式化
```bash
# 使用 UV
uv run black src/
uv run isort src/

# 使用 Makefile
make format
```

### 代码检查
```bash
# 使用 UV
uv run flake8 src/
uv run mypy src/

# 使用 Makefile
make lint
```

### 运行测试
```bash
# 使用 UV
uv run pytest

# 使用 Makefile
make test
```

## 📁 项目结构

```
mineru-service-1.3.10/
├── src/                      # 源代码目录
│   ├── api/                 # API 路由层
│   │   └── text_extraction_router.py  # 文本提取API
│   ├── core/                # 核心配置
│   │   ├── mineru.py       # MinerU核心处理逻辑
│   │   └── settings.py     # 配置管理
│   ├── schema/             # 数据模型
│   │   └── text_extraction.py  # 文本提取数据模型
│   ├── service/            # 业务逻辑层
│   │   ├── text_extraction_service.py  # 文本提取服务
│   │   ├── mineru_service.py           # MinerU服务
│   │   └── base_service.py             # 基础服务类
│   ├── utils/              # 工具函数
│   │   ├── property_utils.py  # 属性工具
│   │   └── type_utils.py     # 类型工具
│   └── main.py             # 应用入口
├── deploy/                 # 部署配置
│   ├── Dockerfile-cpu      # CPU Docker 配置
│   └── Dockerfile-gpu      # GPU Docker 配置
├── bin/                    # 脚本文件
│   ├── start.sh           # 启动脚本
│   ├── docker-build.sh    # Docker构建脚本
│   └── docker-run.sh      # Docker运行脚本
├── config/                 # 配置文件
│   └── properties.json    # 服务属性配置
├── confs/                  # MinerU配置
│   └── layout-optimization.json  # Layout优化配置
├── data/                   # 数据目录（运行时创建）
├── pyproject.toml          # 项目配置和依赖
├── requirements*.txt       # pip 依赖文件
├── install_dependencies.*  # 依赖安装脚本
├── docker-compose.yml      # Docker Compose 配置
├── DOCKER.md              # Docker部署指南
└── Makefile               # 开发任务自动化
```

## 📦 依赖说明

### 🔧 核心依赖
- **FastAPI**: 现代化的高性能 Web 框架
- **Uvicorn**: ASGI 服务器，支持异步处理
- **Pydantic**: 数据验证和设置管理
- **Loguru**: 现代化的日志库，支持结构化日志
- **magic-pdf**: MinerU PDF解析核心库
- **Pillow**: 图像处理库

### 🛠️ 开发依赖
- **pytest**: 现代化的测试框架
- **black**: Python代码格式化工具
- **isort**: 导入语句排序工具
- **flake8**: 代码风格检查工具
- **mypy**: 静态类型检查工具

### 🚀 GPU 依赖（可选）
- **torch**: PyTorch 深度学习框架
- **torchvision**: 计算机视觉库
- **transformers**: Hugging Face 变换器库
- **accelerate**: 模型训练加速库
- **CUDA**: NVIDIA GPU 计算平台支持

### 📋 系统要求
- **Python**: 3.12+
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 2GB 可用空间
- **GPU** (可选): NVIDIA GPU with CUDA 12.6+ support

## 🎯 性能基准

### 处理能力
- **CPU版本**: ~2-5页/秒 (取决于文档复杂度)
- **GPU版本**: ~5-15页/秒 (取决于GPU性能)
- **并发处理**: 支持多文件并发处理
- **内存使用**: 平均每页 ~50-100MB

### 支持格式
- ✅ **PDF**: 标准PDF文档
- ✅ **图片PDF**: 扫描版PDF文档
- ✅ **复杂布局**: 多栏、表格、图表混合布局
- ✅ **多语言**: 中文、英文等多语言支持

## 🔄 更新日志

### v1.3.10 (最新)
- ✨ 新增智能图片标题检测功能
- 🔧 优化Layout后处理逻辑
- 🧹 改进日志系统，过滤无用信息
- 🐳 完善Docker容器化支持
- 📈 提升文本识别准确率
- 🛡️ 增强错误处理机制

### 主要改进
- **智能类型检测**: 更准确的文本类型识别
- **Layout优化**: 改进的版面分析算法
- **性能提升**: 优化的处理流程
- **容器化**: 完整的Docker支持

## 🛠️ 故障排除

### 常见问题

#### 1. **安装相关问题**

**UV 安装失败**
```bash
# 使用官方安装脚本
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows用户
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**依赖冲突**
```bash
# 清理环境重新安装
rm -rf .venv uv.lock
uv sync

# 或使用pip
pip install --force-reinstall -r requirements.txt
```

#### 2. **运行时问题**

**端口被占用**
```bash
# 检查端口占用
netstat -tlnp | grep 8081

# 修改 .env 文件中的端口
SERVER_PORT=8082

# 或使用Docker时指定端口
./bin/docker-run.sh --type cpu --port 8082
```

**内存不足**
```bash
# 减少工作进程数
SERVER_WORKERS=2

# 或在Docker中限制内存
docker run --memory=4g mineru-service:1.3.10-cpu
```

#### 3. **Docker相关问题**

**GPU支持问题**
```bash
# 检查NVIDIA Docker支持
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi

# 安装NVIDIA Container Toolkit
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

**镜像构建失败**
```bash
# 清理Docker缓存
docker system prune -f

# 重新构建（无缓存）
docker build --no-cache -f deploy/Dockerfile-cpu -t mineru-service:latest-cpu .
```

#### 4. **性能优化**

**CPU版本性能优化**
```bash
# 增加工作进程数（根据CPU核心数调整）
SERVER_WORKERS=8

# 启用多线程
export OMP_NUM_THREADS=4
```

**GPU版本优化**
```bash
# 设置GPU内存增长
export CUDA_MEMORY_FRACTION=0.8

# 启用混合精度
export MIXED_PRECISION=true
```

### 📞 获取帮助

如果遇到其他问题，请：

1. 查看 [DOCKER.md](DOCKER.md) 获取详细的Docker部署指南
2. 检查日志输出：`docker logs -f container_name`
3. 访问 [GitHub Issues](https://github.com/your-repo/issues) 报告问题
4. 查看 [API文档](http://localhost:8081/docs) 了解接口使用

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 如何贡献
1. 🍴 Fork 项目
2. 🌿 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 💾 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 📤 推送到分支 (`git push origin feature/AmazingFeature`)
5. 🔄 创建 Pull Request

### 开发规范
- 遵循 PEP 8 代码风格
- 添加适当的类型注解
- 编写测试用例
- 更新相关文档

### 报告问题
- 使用 GitHub Issues 报告 bug
- 提供详细的复现步骤
- 包含系统环境信息

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [MinerU](https://github.com/opendatalab/MinerU) - 核心PDF解析引擎
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化Web框架
- [UV](https://github.com/astral-sh/uv) - 快速Python包管理器

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

[🏠 首页](README.md) | [🐳 Docker指南](DOCKER.md) | [📖 API文档](http://localhost:8081/docs) | [❓ 问题反馈](https://github.com/your-repo/issues)

</div>
